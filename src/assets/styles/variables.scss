// 专业企业蓝色主题设计系统 - 怀川科技官网
:root {
  // 主蓝色系 - 专业企业级配色
  --primary-blue: #0052CC;         // 主蓝 - 深邃专业
  --corporate-blue: #0747A6;       // 企业蓝 - 权威可信
  --ocean-blue: #0065FF;           // 海洋蓝 - 活力创新
  --tech-blue: #2684FF;            // 科技蓝 - 现代时尚
  --azure-blue: #4C9AFF;           // 天蓝 - 清新明亮
  --sky-blue: #B3D4FF;             // 天空蓝 - 轻盈舒适

  // 深色蓝色系 - 用于背景和对比
  --midnight-blue: #0A1629;        // 午夜蓝 - 深度背景
  --navy-blue: #172B4D;            // 海军蓝 - 专业背景
  --dark-blue: #253858;            // 深蓝 - 内容背景
  --slate-blue: #344563;           // 板岩蓝 - 边框线条

  // 辅助色系 - 增强视觉层次
  --electric-blue: #0099FF;        // 电光蓝 - 强调重点
  --crystal-blue: #E6F3FF;         // 水晶蓝 - 轻量背景
  --frost-blue: #F7FAFF;           // 霜蓝 - 最浅背景

  // 科技渐变色
  --neon-cyan: #00D9FF;            // 霓虹青 - 科技感
  --digital-purple: #6366F1;       // 数字紫 - 创新感
  --tech-gradient-start: #0052CC;  // 渐变起点
  --tech-gradient-end: #2684FF;    // 渐变终点

  // 语义化颜色
  --success-green: #36B37E;        // 成功绿
  --warning-orange: #FF991F;       // 警告橙
  --error-red: #DE350B;            // 错误红
  --info-blue: var(--tech-blue);   // 信息蓝

  // 中性色系 - 精确灰度
  --white: #FFFFFF;
  --gray-50: #FAFBFC;              // 最浅灰
  --gray-100: #F4F5F7;             // 浅灰
  --gray-200: #EBECF0;             // 边框灰
  --gray-300: #DFE1E6;             // 分割线灰
  --gray-400: #C1C7D0;             // 禁用灰
  --gray-500: #A5ADBA;             // 占位符灰
  --gray-600: #7A869A;             // 次要文字灰
  --gray-700: #6B778C;             // 主要文字灰
  --gray-800: #42526E;             // 标题灰
  --gray-900: #253858;             // 深色文字
  --black: #091E42;                // 纯黑

  // 透明度色彩 - 精确控制
  --primary-alpha-5: rgba(0, 82, 204, 0.05);
  --primary-alpha-10: rgba(0, 82, 204, 0.1);
  --primary-alpha-15: rgba(0, 82, 204, 0.15);
  --primary-alpha-20: rgba(0, 82, 204, 0.2);
  --primary-alpha-30: rgba(0, 82, 204, 0.3);
  --primary-alpha-40: rgba(0, 82, 204, 0.4);
  --primary-alpha-60: rgba(0, 82, 204, 0.6);
  --primary-alpha-80: rgba(0, 82, 204, 0.8);

  --tech-alpha-10: rgba(38, 132, 255, 0.1);
  --tech-alpha-20: rgba(38, 132, 255, 0.2);
  --tech-alpha-30: rgba(38, 132, 255, 0.3);
  --tech-alpha-50: rgba(38, 132, 255, 0.5);

  // 白色透明度
  --white-alpha-10: rgba(255, 255, 255, 0.1);
  --white-alpha-20: rgba(255, 255, 255, 0.2);
  --white-alpha-30: rgba(255, 255, 255, 0.3);
  --white-alpha-40: rgba(255, 255, 255, 0.4);
  --white-alpha-50: rgba(255, 255, 255, 0.5);

  --dark-alpha-80: rgba(9, 30, 66, 0.8);
  --dark-alpha-60: rgba(9, 30, 66, 0.6);
  --dark-alpha-40: rgba(9, 30, 66, 0.4);
  --dark-alpha-20: rgba(9, 30, 66, 0.2);
  --dark-alpha-10: rgba(9, 30, 66, 0.1);

  // 高级渐变系统
  --gradient-primary: linear-gradient(135deg, var(--primary-blue) 0%, var(--corporate-blue) 100%);
  --gradient-ocean: linear-gradient(135deg, var(--ocean-blue) 0%, var(--tech-blue) 100%);
  --gradient-sky: linear-gradient(135deg, var(--tech-blue) 0%, var(--azure-blue) 100%);
  --gradient-innovation: linear-gradient(135deg, var(--tech-blue) 0%, var(--digital-purple) 100%);
  --gradient-neon: linear-gradient(135deg, var(--electric-blue) 0%, var(--neon-cyan) 100%);
  --gradient-subtle: linear-gradient(135deg, var(--crystal-blue) 0%, var(--frost-blue) 100%);

  // 动态渐变 - 用于动画
  --gradient-animated: linear-gradient(45deg,
    var(--primary-blue) 0%,
    var(--tech-blue) 25%,
    var(--electric-blue) 50%,
    var(--tech-blue) 75%,
    var(--primary-blue) 100%);

  // 玻璃形态渐变
  --gradient-glass: linear-gradient(135deg,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0.1) 100%);

  // 深度渐变 - 用于背景
  --gradient-depth: linear-gradient(180deg,
    var(--midnight-blue) 0%,
    var(--navy-blue) 50%,
    var(--dark-blue) 100%);

  // 阴影层级系统 - 企业级精确控制
  --shadow-xs: 0 1px 2px var(--dark-alpha-10);
  --shadow-sm: 0 2px 4px var(--dark-alpha-10), 0 1px 2px var(--dark-alpha-20);
  --shadow-md: 0 4px 8px var(--dark-alpha-15), 0 2px 4px var(--dark-alpha-10);
  --shadow-lg: 0 8px 16px var(--dark-alpha-15), 0 4px 8px var(--dark-alpha-10);
  --shadow-xl: 0 16px 32px var(--dark-alpha-20), 0 8px 16px var(--dark-alpha-10);
  --shadow-2xl: 0 24px 48px var(--dark-alpha-25), 0 12px 24px var(--dark-alpha-15);

  // 内阴影
  --shadow-inner: inset 0 2px 4px var(--dark-alpha-10);
  --shadow-inner-md: inset 0 4px 8px var(--dark-alpha-15);

  // 发光效果 - 蓝色系专业发光
  --glow-primary: 0 0 20px var(--primary-alpha-30), 0 0 40px var(--primary-alpha-20);
  --glow-tech: 0 0 24px var(--tech-alpha-30), 0 0 48px var(--tech-alpha-20);
  --glow-ocean: 0 0 28px rgba(0, 101, 255, 0.25), 0 0 56px rgba(0, 101, 255, 0.15);
  --glow-neon: 0 0 32px rgba(0, 217, 255, 0.4), 0 0 64px rgba(0, 217, 255, 0.2);

  // 交互式阴影
  --shadow-hover: 0 8px 24px var(--primary-alpha-20), 0 4px 12px var(--primary-alpha-30);
  --shadow-active: 0 2px 8px var(--primary-alpha-30), 0 1px 4px var(--primary-alpha-40);
  --shadow-focus: 0 0 0 3px var(--primary-alpha-20), var(--shadow-md);

  // 字体系统 - 现代企业级层次
  --text-xs: 0.75rem;      // 12px - 标签文字
  --text-sm: 0.875rem;     // 14px - 辅助信息
  --text-base: 1rem;       // 16px - 正文内容
  --text-lg: 1.125rem;     // 18px - 重要内容
  --text-xl: 1.25rem;      // 20px - 小标题
  --text-2xl: 1.5rem;      // 24px - 中标题
  --text-3xl: 1.875rem;    // 30px - 大标题
  --text-4xl: 2.25rem;     // 36px - 主标题
  --text-5xl: 3rem;        // 48px - 超大标题
  --text-6xl: 3.75rem;     // 60px - 展示标题
  --text-7xl: 4.5rem;      // 72px - 巨型标题
  --text-8xl: 6rem;        // 96px - 英雄标题

  // 字体粗细
  --font-thin: 100;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;

  // 行高系统
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  // 字母间距
  --tracking-tighter: -0.05em;
  --tracking-tight: -0.025em;
  --tracking-normal: 0em;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;
  --tracking-widest: 0.1em;

  // 间距系统 - 精确控制网格
  --space-0: 0rem;         // 0px
  --space-px: 0.0625rem;   // 1px
  --space-0-5: 0.125rem;   // 2px
  --space-1: 0.25rem;      // 4px
  --space-1-5: 0.375rem;   // 6px
  --space-2: 0.5rem;       // 8px
  --space-2-5: 0.625rem;   // 10px
  --space-3: 0.75rem;      // 12px
  --space-3-5: 0.875rem;   // 14px
  --space-4: 1rem;         // 16px
  --space-5: 1.25rem;      // 20px
  --space-6: 1.5rem;       // 24px
  --space-7: 1.75rem;      // 28px
  --space-8: 2rem;         // 32px
  --space-9: 2.25rem;      // 36px
  --space-10: 2.5rem;      // 40px
  --space-11: 2.75rem;     // 44px
  --space-12: 3rem;        // 48px
  --space-14: 3.5rem;      // 56px
  --space-16: 4rem;        // 64px
  --space-20: 5rem;        // 80px
  --space-24: 6rem;        // 96px
  --space-28: 7rem;        // 112px
  --space-32: 8rem;        // 128px
  --space-36: 9rem;        // 144px
  --space-40: 10rem;       // 160px
  --space-44: 11rem;       // 176px
  --space-48: 12rem;       // 192px
  --space-52: 13rem;       // 208px
  --space-56: 14rem;       // 224px
  --space-60: 15rem;       // 240px
  --space-64: 16rem;       // 256px
  --space-72: 18rem;       // 288px
  --space-80: 20rem;       // 320px
  --space-96: 24rem;       // 384px

  // 圆角系统 - 现代圆润设计
  --radius-none: 0rem;
  --radius-sm: 0.25rem;    // 4px
  --radius-md: 0.375rem;   // 6px
  --radius-lg: 0.5rem;     // 8px
  --radius-xl: 0.75rem;    // 12px
  --radius-2xl: 1rem;      // 16px
  --radius-3xl: 1.5rem;    // 24px
  --radius-4xl: 2rem;      // 32px
  --radius-full: 9999px;

  // 边框宽度
  --border-0: 0px;
  --border: 1px;
  --border-2: 2px;
  --border-4: 4px;
  --border-8: 8px;

  // 动画系统 - 专业动效
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;

  // 缓动函数 - 自然动效
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-smooth: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-bounce: cubic-bezier(0.68, -0.6, 0.32, 1.6);
  --ease-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);

  // 组合过渡
  --transition-fast: var(--duration-150) var(--ease-out);
  --transition-base: var(--duration-300) var(--ease-in-out);
  --transition-slow: var(--duration-500) var(--ease-in-out);
  --transition-smooth: var(--duration-300) var(--ease-smooth);
  --transition-bounce: var(--duration-700) var(--ease-bounce);
  --transition-all: all var(--duration-300) var(--ease-in-out);

  // Z-index 层级系统 - 清晰层次管理
  --z-hide: -1;
  --z-auto: auto;
  --z-base: 0;
  --z-docked: 10;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-banner: 1030;
  --z-overlay: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-skipLink: 1070;
  --z-toast: 1080;
  --z-tooltip: 1090;
  --z-max: 2147483647;
}

// SCSS 变量映射 - 新蓝色主题系统
$primary-blue: var(--primary-blue);
$corporate-blue: var(--corporate-blue);
$ocean-blue: var(--ocean-blue);
$tech-blue: var(--tech-blue);
$azure-blue: var(--azure-blue);
$sky-blue: var(--sky-blue);

$midnight-blue: var(--midnight-blue);
$navy-blue: var(--navy-blue);
$dark-blue: var(--dark-blue);
$slate-blue: var(--slate-blue);

$electric-blue: var(--electric-blue);
$crystal-blue: var(--crystal-blue);
$frost-blue: var(--frost-blue);
$neon-cyan: var(--neon-cyan);
$digital-purple: var(--digital-purple);

$success-green: var(--success-green);
$warning-orange: var(--warning-orange);
$error-red: var(--error-red);
$info-blue: var(--info-blue);

// 高级混合器系统 - 专业企业级效果
@mixin glass-morphism($opacity: 0.15, $blur: 20px, $border-alpha: 0.2) {
  background: rgba(255, 255, 255, $opacity);
  backdrop-filter: blur($blur) saturate(180%);
  -webkit-backdrop-filter: blur($blur) saturate(180%);
  border: 1px solid rgba(255, 255, 255, $border-alpha);
  box-shadow: var(--shadow-lg);
}

@mixin blue-glass($opacity: 0.1, $blur: 16px) {
  background: linear-gradient(135deg,
    rgba(0, 82, 204, $opacity) 0%,
    rgba(38, 132, 255, $opacity * 0.7) 100%);
  backdrop-filter: blur($blur) saturate(150%);
  -webkit-backdrop-filter: blur($blur) saturate(150%);
  border: 1px solid rgba(38, 132, 255, 0.2);
}

@mixin premium-shadow($color: var(--primary-blue)) {
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.12),
    0 1px 2px rgba(0, 0, 0, 0.24),
    0 0 20px rgba($color, 0.15);
}

@mixin tech-ripple($color: var(--tech-blue), $duration: 0.6s) {
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba($color, 0.3),
      transparent
    );
    transition: left $duration var(--ease-smooth);
  }

  &:hover::before {
    left: 100%;
  }
}

@mixin floating-animation($duration: 3s, $distance: 10px, $delay: 0s) {
  animation: floating-#{$duration}-#{$distance} $duration var(--ease-in-out) infinite;
  animation-delay: $delay;

  @keyframes floating-#{$duration}-#{$distance} {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-#{$distance});
    }
  }
}

@mixin gradient-text($gradient: var(--gradient-ocean)) {
  background: $gradient;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

@mixin card-hover($scale: 1.02, $y: -4px) {
  transition: var(--transition-all);

  &:hover {
    transform: translateY($y) scale($scale);
    box-shadow: var(--shadow-hover);
  }

  &:active {
    transform: translateY(-2px) scale(0.98);
    box-shadow: var(--shadow-active);
  }
}

@mixin button-primary($bg: var(--gradient-primary)) {
  background: $bg;
  color: var(--white);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-3) var(--space-6);
  font-weight: var(--font-medium);
  font-size: var(--text-base);
  cursor: pointer;
  transition: var(--transition-all);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transition: left var(--duration-500) var(--ease-smooth);
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0);
    box-shadow: var(--shadow-active);
  }

  &:focus {
    outline: none;
    box-shadow: var(--shadow-focus);
  }
}

@mixin responsive-grid($columns: auto-fit, $min-width: 300px, $gap: var(--space-6)) {
  display: grid;
  grid-template-columns: repeat($columns, minmax($min-width, 1fr));
  gap: $gap;
}

@mixin section-padding($top: var(--space-20), $bottom: var(--space-20)) {
  padding-top: $top;
  padding-bottom: $bottom;

  @media (max-width: 768px) {
    padding-top: calc(#{$top} * 0.7);
    padding-bottom: calc(#{$bottom} * 0.7);
  }

  @media (max-width: 480px) {
    padding-top: calc(#{$top} * 0.5);
    padding-bottom: calc(#{$bottom} * 0.5);
  }
}