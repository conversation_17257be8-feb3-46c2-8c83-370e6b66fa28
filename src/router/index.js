import { createRouter, createWebHistory } from "vue-router";

// 导入页面组件
const Home = () => import("@/views/Home.vue");
const About = () => import("@/views/About.vue");
const Products = () => import("@/views/Products.vue");
const Solutions = () => import("@/views/Solutions.vue");

// 定义路由
const routes = [
  {
    path: "/",
    name: "Home",
    component: Home,
  },
  {
    path: "/about",
    name: "About",
    component: About,
  },
  {
    path: "/products",
    name: "Products",
    component: Products,
  },
  {
    path: "/solutions",
    name: "Solutions",
    component: Solutions,
  },
];

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的位置（比如浏览器前进后退），优先使用保存的位置
    if (savedPosition) {
      return savedPosition;
    }

    // 如果路由有锚点，滚动到锚点位置
    if (to.hash) {
      return {
        el: to.hash,
        behavior: "smooth",
      };
    }

    // 默认滚动到顶部，添加延迟确保异步组件和DOM渲染完成
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          left: 0,
          top: 0,
          behavior: "smooth",
        });
      }, 150);
    });
  },
});

export default router;
