<script setup>
import NavBar from '@/components/NavBar.vue';

defineOptions({
  name: "App",
});
</script>

<template>
  <div class="app-container">
    <NavBar />
    <main>
      <!-- 路由视图 -->
      <router-view />
    </main>
    <footer class="footer">
      <div class="container">
        <div class="copyright">Copyright © 2025 怀川科技（北京）有限公司版权所有</div>
      </div>
    </footer>
  </div>
</template>

<style lang="scss">
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;

  main {
    flex: 1;
  }

  .footer {
    background-color: #263238;
    color: rgba(255, 255, 255, 0.7);
    padding: 20px 0;
    text-align: center;

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    .copyright {
      font-size: 14px;
    }
  }
}
</style>
