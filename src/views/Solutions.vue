<script setup>
import { Icon } from "@iconify/vue";
import { ref, onMounted } from "vue";

defineOptions({
  name: "Solutions",
});

// 页面状态
const isLoaded = ref(false);

// 行业痛点数据
const industryPainPoints = ref([
  {
    icon: 'mdi:water-alert',
    title: '水资源过度开发',
    description: '对水资源过度、无序的开发利用，造成河湖生态失衡，引发水质恶化、岸线崩塌、水体萎缩、洪水泛滥等问题'
  },
  {
    icon: 'mdi:map-marker-path',
    title: '流域系统复杂',
    description: '流域包含复杂的水文演变、物质输移和人类活动等过程，相互影响，使得河湖问题的诊断、溯源和治理面临挑战'
  },
  {
    icon: 'mdi:alert-circle',
    title: '灾害威胁严重',
    description: '暴雨、洪水、干旱、污染等河湖问题严重威胁人民生命财产安全，舆情传播迅速，加剧负面影响'
  },
  {
    icon: 'mdi:chart-line-variant',
    title: '治理能力不足',
    description: '传统治理方式难以应对复杂的流域问题，缺乏智能化手段和科学决策支撑'
  }
]);

// 核心解决方案数据 - 基于真实的人工智能+水利解决方案
const coreSolutions = ref([
  {
    icon: 'mdi:brain',
    category: '流域智能体',
    title: '人工智能+水利解决方案',
    description: '通过整合空天地水工等多源感知数据，依托行业机理模型、人工智能模型、三维空间模型等，构建与现实映射的流域数字孪生，生成流域智能体',
    benefits: ['持续提升感知能力', '增强认知决策能力', '优化行动执行能力', '辅助任务处置决策'],
    technologies: ['多源感知数据', '行业机理模型', '人工智能模型', '三维空间模型'],
    projectCount: 50,
    clientCount: 25
  }
]);

// 方案特点数据
const solutionFeatures = ref([
  {
    icon: 'mdi:target',
    title: '目标驱动',
    description: '以流域或区域水资源开发利用、防洪减灾等目标驱动流域智能体进行目标分解和任务规划，结合知识分析和模型推理，给出业务场景的最优决策方案',
    color: 'var(--primary-blue)'
  },
  {
    icon: 'mdi:account-group',
    title: '群体智能',
    description: '以流域作为一个宏观智能体，集合了流域内的工程、测站、设备、管理单元等具体智能体，各智能体基于网络相互联动，形成群体智能',
    color: 'var(--electric-blue)'
  },
  {
    icon: 'mdi:account-tie',
    title: '人机协同',
    description: '人类专家与流域智能体协同工作，负责监督智能体的学习训练，对任务处置结果进行复核确认，根据推荐方案进行决策指挥等',
    color: 'var(--neon-cyan)'
  },
  {
    icon: 'mdi:merge',
    title: '虚实融合',
    description: '流域智能体通过流域数字孪生模拟和演练工作技能，在参与业务管理活动和对物理流域的感知、交互过程中持续积累知识和优化模型',
    color: 'var(--success-green)'
  }
]);

// 应用场景数据
const applicationScenarios = ref([
  {
    icon: 'mdi:shield-alert',
    title: '水旱灾害防御',
    description: '基于流域智能体的洪水预警预报和应急响应决策支持',
    color: 'var(--warning-orange)'
  },
  {
    icon: 'mdi:water-pump',
    title: '水资源配置',
    description: '智能化水资源调度和优化配置，提升水资源利用效率',
    color: 'var(--primary-blue)'
  },
  {
    icon: 'mdi:leaf',
    title: '水环境治理',
    description: '水环境质量监测分析和污染治理决策支持',
    color: 'var(--success-green)'
  },
  {
    icon: 'mdi:nature',
    title: '水生态保护',
    description: '水生态系统健康评估和保护修复指导',
    color: 'var(--neon-cyan)'
  }
]);

// 事件处理
const handleSolutionDetail = (solution) => {
  console.log('查看解决方案详情:', solution);
};

const handleConsultation = () => {
  console.log('预约专家咨询');
};

const handleDemo = () => {
  console.log('观看演示视频');
};

// 页面加载动画
onMounted(() => {
  setTimeout(() => {
    isLoaded.value = true;
  }, 100);
});
</script>

<template>
  <div class="solutions" :class="{ 'loaded': isLoaded }">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-background">
        <div class="hero-overlay"></div>
        <div class="hero-particles">
          <div v-for="i in 15" :key="i" :class="`particle particle-${i}`"></div>
        </div>
        <div class="hero-network">
          <div v-for="i in 8" :key="i" :class="`network-node node-${i}`"></div>
          <svg class="network-lines" viewBox="0 0 100 100">
            <path class="network-path" d="M20,30 Q50,10 80,30" />
            <path class="network-path" d="M15,60 Q40,40 65,60" />
            <path class="network-path" d="M30,80 Q60,60 90,80" />
          </svg>
        </div>
      </div>

      <div class="container">
        <div class="hero-content">
          <div class="hero-badge fade-in-up">
            <Icon icon="mdi:lightbulb-on-outline" class="badge-icon" />
            <span>智慧水利数字化转型</span>
          </div>

          <h1 class="hero-title fade-in-up delay-1">
            <span class="title-line">人工智能+水利</span>
            <span class="title-line title-highlight">解决方案</span>
          </h1>

          <p class="hero-subtitle fade-in-up delay-2">
            通过整合空天地水工等多源感知数据，构建与现实映射的<strong>流域数字孪生</strong>，<br>
            生成流域智能体，显著提升流域治理能力
          </p>

          <div class="hero-features fade-in-up delay-3">
            <div class="feature-item">
              <div class="feature-icon">
                <Icon icon="mdi:chart-timeline-variant" />
              </div>
              <div class="feature-text">
                <div class="feature-title">实时监控</div>
                <div class="feature-desc">7×24小时全天候监测</div>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <Icon icon="mdi:brain" />
              </div>
              <div class="feature-text">
                <div class="feature-title">智能分析</div>
                <div class="feature-desc">AI驱动的预测分析</div>
              </div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">
                <Icon icon="mdi:shield-check" />
              </div>
              <div class="feature-text">
                <div class="feature-title">安全可靠</div>
                <div class="feature-desc">企业级安全保障</div>
              </div>
            </div>
          </div>
        </div>

        <div class="hero-visual fade-in-up delay-4">
          <div class="visual-container">
            <div class="solution-hub">
              <div class="hub-center">
                <Icon icon="mdi:water-circle" class="hub-icon" />
                <div class="hub-pulse"></div>
              </div>
              <div class="hub-orbits">
                <div class="orbit orbit-1">
                  <div class="orbit-item item-1">
                    <Icon icon="mdi:chart-line" />
                  </div>
                </div>
                <div class="orbit orbit-2">
                  <div class="orbit-item item-2">
                    <Icon icon="mdi:cloud-outline" />
                  </div>
                  <div class="orbit-item item-3">
                    <Icon icon="mdi:shield-check" />
                  </div>
                </div>
                <div class="orbit orbit-3">
                  <div class="orbit-item item-4">
                    <Icon icon="mdi:trending-up" />
                  </div>
                  <div class="orbit-item item-5">
                    <Icon icon="mdi:database" />
                  </div>
                  <div class="orbit-item item-6">
                    <Icon icon="mdi:cog" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 行业痛点 -->
    <section class="industry-pain-points">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:alert-circle" />
            <span>行业痛点</span>
          </div>
          <h2 class="section-title">当前面临的挑战</h2>
          <p class="section-subtitle">河湖问题在水里，根源在岸上，需要水岸同治，从流域角度实施系统治理</p>
        </div>

        <div class="pain-points-grid">
          <div
            v-for="(point, index) in industryPainPoints"
            :key="index"
            class="pain-point-card"
          >
            <div class="point-icon">
              <Icon :icon="point.icon" />
            </div>
            <h3 class="point-title">{{ point.title }}</h3>
            <p class="point-description">{{ point.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心解决方案 -->
    <section class="core-solutions">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:brain" />
            <span>核心方案</span>
          </div>
          <h2 class="section-title">流域智能体解决方案</h2>
          <p class="section-subtitle">嵌入水旱灾害防御、水资源配置、水环境治理、水生态保护等业务应用</p>
        </div>

        <div class="solutions-grid">
          <div
            v-for="(solution, index) in coreSolutions"
            :key="index"
            class="solution-card"
            @click="handleSolutionDetail(solution)"
          >
            <div class="card-header">
              <div class="solution-icon">
                <Icon :icon="solution.icon" />
              </div>
              <div class="solution-category">{{ solution.category }}</div>
            </div>

            <div class="card-content">
              <h3 class="solution-title">{{ solution.title }}</h3>
              <p class="solution-description">{{ solution.description }}</p>

              <div class="solution-benefits">
                <div class="benefits-title">核心价值</div>
                <div class="benefits-list">
                  <div class="benefit" v-for="benefit in solution.benefits" :key="benefit">
                    <Icon icon="mdi:check-circle" class="benefit-icon" />
                    <span>{{ benefit }}</span>
                  </div>
                </div>
              </div>

              <div class="solution-tech">
                <div class="tech-title">技术栈</div>
                <div class="tech-tags">
                  <span class="tech-tag" v-for="tech in solution.technologies" :key="tech">{{ tech }}</span>
                </div>
              </div>
            </div>

            <div class="card-footer">
              <div class="solution-metrics">
                <div class="metric">
                  <span class="metric-number">{{ solution.projectCount }}+</span>
                  <span class="metric-label">成功案例</span>
                </div>
                <div class="metric">
                  <span class="metric-number">{{ solution.clientCount }}+</span>
                  <span class="metric-label">客户数量</span>
                </div>
              </div>
              <button class="explore-btn">
                <span>了解详情</span>
                <Icon icon="mdi:arrow-right" class="btn-arrow" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 方案特点 -->
    <section class="solution-features">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:star-four-points" />
            <span>方案特点</span>
          </div>
          <h2 class="section-title">四大核心特点</h2>
          <p class="section-subtitle">目标驱动、群体智能、人机协同、虚实融合的创新解决方案</p>
        </div>

        <div class="features-grid">
          <div
            v-for="(feature, index) in solutionFeatures"
            :key="index"
            class="feature-card"
            :style="{ '--feature-color': feature.color }"
          >
            <div class="feature-icon">
              <Icon :icon="feature.icon" />
            </div>
            <h3 class="feature-title">{{ feature.title }}</h3>
            <p class="feature-description">{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 应用场景 -->
    <section class="application-scenarios">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:application" />
            <span>应用场景</span>
          </div>
          <h2 class="section-title">业务应用领域</h2>
          <p class="section-subtitle">嵌入水利行业核心业务场景，提供智能化决策支持</p>
        </div>

        <div class="scenarios-grid">
          <div
            v-for="(scenario, index) in applicationScenarios"
            :key="index"
            class="scenario-card"
            :style="{ '--scenario-color': scenario.color }"
          >
            <div class="scenario-icon">
              <Icon :icon="scenario.icon" />
            </div>
            <h3 class="scenario-title">{{ scenario.title }}</h3>
            <p class="scenario-description">{{ scenario.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA 区域 -->
    <section class="cta-section">
      <div class="cta-background">
        <div class="cta-overlay"></div>
        <div class="cta-grid">
          <div v-for="i in 30" :key="i" class="grid-item"></div>
        </div>
      </div>

      <div class="container">
        <div class="cta-content">
          <div class="cta-text">
            <div class="cta-badge">
              <Icon icon="mdi:rocket-launch" />
              <span>开始您的数字化之旅</span>
            </div>
            <h2 class="cta-title">开启流域智能化治理新时代</h2>
            <p class="cta-subtitle">
              我们的专业团队将为您提供<strong>流域数字孪生构建</strong>和<strong>智能体定制开发</strong><br>
              以水利现代化推动人与自然和谐共生
            </p>

            <div class="cta-benefits">
              <div class="benefit-item">
                <Icon icon="mdi:account-tie" />
                <span>专家1对1咨询</span>
              </div>
              <div class="benefit-item">
                <Icon icon="mdi:file-document-outline" />
                <span>免费方案评估</span>
              </div>
              <div class="benefit-item">
                <Icon icon="mdi:clock-fast" />
                <span>快速响应服务</span>
              </div>
            </div>
          </div>

          <div class="cta-actions">
            <button class="btn-primary large" @click="handleConsultation">
              <Icon icon="mdi:account-tie" class="btn-icon" />
              <span>预约专家咨询</span>
              <Icon icon="mdi:arrow-right" class="btn-arrow" />
            </button>
            <button class="btn-secondary large" @click="handleDemo">
              <Icon icon="mdi:play-circle" class="btn-icon" />
              <span>观看演示视频</span>
            </button>

            <div class="contact-info">
              <div class="contact-item">
                <Icon icon="mdi:phone" />
                <span>186-1032-4200</span>
              </div>
              <div class="contact-item">
                <Icon icon="mdi:email" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
@use "@/assets/styles/variables.scss" as *;

.solutions {
  min-height: 100vh;
  color: var(--gray-800);
  overflow-x: hidden;

  // 页面加载动画
  opacity: 0;
  transition: opacity var(--duration-700) var(--ease-out);

  &.loaded {
    opacity: 1;

    .fade-in-up {
      opacity: 1;
      transform: translateY(0);

      &.delay-1 { animation-delay: 0.1s; }
      &.delay-2 { animation-delay: 0.2s; }
      &.delay-3 { animation-delay: 0.3s; }
      &.delay-4 { animation-delay: 0.4s; }
    }
  }
}

// 通用动画类
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s var(--ease-smooth) forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 通用容器
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);

  @media (max-width: 768px) {
    padding: 0 var(--space-4);
  }
}

// 英雄区域
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;

  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-depth);
    z-index: -1;
  }

  .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      var(--primary-alpha-90) 0%,
      var(--corporate-blue) 100%
    );
    z-index: -1;
  }

  .hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;

    .particle {
      position: absolute;
      width: 4px;
      height: 4px;
      background: var(--electric-blue);
      border-radius: 50%;
      opacity: 0.6;

      &.particle-1 { top: 20%; left: 10%; animation: float 6s infinite 0s; }
      &.particle-2 { top: 30%; left: 80%; animation: float 8s infinite 1s; }
      &.particle-3 { top: 60%; left: 15%; animation: float 7s infinite 2s; }
      &.particle-4 { top: 80%; left: 70%; animation: float 9s infinite 3s; }
      &.particle-5 { top: 40%; left: 90%; animation: float 6s infinite 4s; }
      &.particle-6 { top: 70%; left: 25%; animation: float 8s infinite 5s; }
      &.particle-7 { top: 15%; left: 60%; animation: float 7s infinite 1.5s; }
      &.particle-8 { top: 85%; left: 40%; animation: float 9s infinite 2.5s; }
      &.particle-9 { top: 50%; left: 5%; animation: float 6s infinite 3.5s; }
      &.particle-10 { top: 25%; left: 95%; animation: float 8s infinite 4.5s; }
      &.particle-11 { top: 75%; left: 55%; animation: float 7s infinite 0.5s; }
      &.particle-12 { top: 35%; left: 30%; animation: float 9s infinite 1.8s; }
      &.particle-13 { top: 65%; left: 85%; animation: float 6s infinite 2.8s; }
      &.particle-14 { top: 45%; left: 75%; animation: float 8s infinite 3.8s; }
      &.particle-15 { top: 55%; left: 45%; animation: float 7s infinite 4.8s; }
    }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.6; }
    25% { transform: translateY(-20px) rotate(90deg); opacity: 1; }
    50% { transform: translateY(-10px) rotate(180deg); opacity: 0.8; }
    75% { transform: translateY(-30px) rotate(270deg); opacity: 1; }
  }

  .hero-network {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;

    .network-node {
      position: absolute;
      width: 8px;
      height: 8px;
      background: var(--neon-cyan);
      border-radius: 50%;
      box-shadow: 0 0 20px var(--neon-cyan);

      &.node-1 { top: 25%; left: 20%; animation: pulse 3s infinite 0s; }
      &.node-2 { top: 40%; left: 75%; animation: pulse 3s infinite 0.5s; }
      &.node-3 { top: 65%; left: 30%; animation: pulse 3s infinite 1s; }
      &.node-4 { top: 80%; left: 80%; animation: pulse 3s infinite 1.5s; }
      &.node-5 { top: 15%; left: 85%; animation: pulse 3s infinite 2s; }
      &.node-6 { top: 55%; left: 10%; animation: pulse 3s infinite 2.5s; }
      &.node-7 { top: 35%; left: 50%; animation: pulse 3s infinite 1.2s; }
      &.node-8 { top: 70%; left: 65%; animation: pulse 3s infinite 1.8s; }
    }

    .network-lines {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0.3;

      .network-path {
        fill: none;
        stroke: var(--electric-blue);
        stroke-width: 0.5;
        stroke-dasharray: 5,5;
        animation: dash 20s linear infinite;
      }
    }
  }

  @keyframes pulse {
    0%, 100% { opacity: 0.4; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.5); }
  }

  @keyframes dash {
    to { stroke-dashoffset: -100; }
  }

  .container {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    align-items: center;

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: var(--space-12);
      text-align: center;
    }
  }

  .hero-content {
    color: var(--white);

    .hero-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--white-alpha-20);
      border: 1px solid var(--white-alpha-30);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      margin-bottom: var(--space-6);
      @include glass-morphism(0.1, 16px, 0.2);

      .badge-icon {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .hero-title {
      font-size: var(--text-5xl);
      font-weight: var(--font-bold);
      line-height: var(--leading-tight);
      margin-bottom: var(--space-6);

      @media (max-width: 768px) {
        font-size: var(--text-4xl);
      }

      .title-line {
        display: block;

        &.title-highlight {
          @include gradient-text(var(--gradient-neon));
        }
      }
    }

    .hero-subtitle {
      font-size: var(--text-lg);
      line-height: var(--leading-relaxed);
      opacity: 0.9;
      margin-bottom: var(--space-8);

      strong {
        color: var(--electric-blue);
        font-weight: var(--font-bold);
      }
    }

    .hero-features {
      display: flex;
      gap: var(--space-8);
      margin-bottom: var(--space-8);

      @media (max-width: 640px) {
        flex-direction: column;
        gap: var(--space-4);
      }

      .feature-item {
        display: flex;
        align-items: center;
        gap: var(--space-3);

        .feature-icon {
          width: 50px;
          height: 50px;
          background: var(--gradient-glass);
          @include glass-morphism(0.2, 12px, 0.3);
          border-radius: var(--radius-xl);
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--electric-blue);
          font-size: var(--text-xl);
        }

        .feature-text {
          .feature-title {
            font-size: var(--text-base);
            font-weight: var(--font-bold);
            margin-bottom: var(--space-1);
          }

          .feature-desc {
            font-size: var(--text-sm);
            opacity: 0.8;
          }
        }
      }
    }
  }

  .hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;

    .visual-container {
      position: relative;
      width: 400px;
      height: 400px;

      @media (max-width: 768px) {
        width: 300px;
        height: 300px;
      }
    }

    .solution-hub {
      position: relative;
      width: 100%;
      height: 100%;

      .hub-center {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100px;
        height: 100px;
        background: var(--gradient-primary);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: var(--glow-primary);
        z-index: 3;

        .hub-icon {
          font-size: var(--text-4xl);
          color: var(--white);
        }

        .hub-pulse {
          position: absolute;
          top: -20px;
          left: -20px;
          width: 140px;
          height: 140px;
          border: 2px solid var(--electric-blue);
          border-radius: 50%;
          animation: hubPulse 3s infinite;
        }
      }

      .hub-orbits {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;

        .orbit {
          position: absolute;
          top: 50%;
          left: 50%;
          border: 1px solid var(--primary-alpha-30);
          border-radius: 50%;
          transform: translate(-50%, -50%);

          &.orbit-1 {
            width: 180px;
            height: 180px;
            animation: rotate 20s linear infinite;
          }

          &.orbit-2 {
            width: 260px;
            height: 260px;
            animation: rotate 30s linear infinite reverse;
          }

          &.orbit-3 {
            width: 340px;
            height: 340px;
            animation: rotate 40s linear infinite;
          }

          .orbit-item {
            position: absolute;
            width: 40px;
            height: 40px;
            background: var(--gradient-glass);
            @include glass-morphism(0.2, 12px, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--electric-blue);
            font-size: var(--text-lg);
            box-shadow: var(--glow-neon);

            &.item-1 {
              top: -20px;
              left: 50%;
              transform: translateX(-50%);
            }

            &.item-2 {
              top: -20px;
              left: 50%;
              transform: translateX(-50%);
            }

            &.item-3 {
              bottom: -20px;
              left: 50%;
              transform: translateX(-50%);
            }

            &.item-4 {
              top: -20px;
              left: 50%;
              transform: translateX(-50%);
            }

            &.item-5 {
              right: -20px;
              top: 50%;
              transform: translateY(-50%);
            }

            &.item-6 {
              bottom: -20px;
              left: 50%;
              transform: translateX(-50%);
            }
          }
        }
      }
    }
  }

  @keyframes hubPulse {
    0%, 100% { opacity: 0.5; transform: translate(-50%, -50%) scale(1); }
    50% { opacity: 0.2; transform: translate(-50%, -50%) scale(1.2); }
  }

  @keyframes rotate {
    from { transform: translate(-50%, -50%) rotate(0deg); }
    to { transform: translate(-50%, -50%) rotate(360deg); }
  }
}

// 行业痛点区域
.industry-pain-points {
  @include section-padding(var(--space-20), var(--space-20));
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;

  // 背景装饰
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at 30% 70%, rgba(239, 68, 68, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(245, 101, 101, 0.05) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
    z-index: 0;
  }

  @keyframes float {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(-20px, -20px) rotate(1deg); }
    66% { transform: translate(20px, -10px) rotate(-1deg); }
  }

  .container {
    position: relative;
    z-index: 1;
  }

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-3) var(--space-5);
      background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(245, 101, 101, 0.1) 100%);
      border: 2px solid rgba(239, 68, 68, 0.2);
      border-radius: var(--radius-3xl);
      font-size: var(--text-sm);
      font-weight: var(--font-bold);
      color: #dc2626;
      margin-bottom: var(--space-8);
      backdrop-filter: blur(10px);
      box-shadow: 0 8px 32px rgba(239, 68, 68, 0.1);
      transition: var(--transition-all);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(239, 68, 68, 0.15);
      }

      svg {
        font-size: var(--text-lg);
        color: #dc2626;
        filter: drop-shadow(0 2px 4px rgba(239, 68, 68, 0.2));
      }
    }

    .section-title {
      font-size: var(--text-5xl);
      font-weight: var(--font-black);
      margin-bottom: var(--space-6);
      background: linear-gradient(135deg, #1e293b 0%, #475569 50%, #64748b 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      letter-spacing: -0.02em;

      @media (max-width: 768px) {
        font-size: var(--text-4xl);
      }
    }

    .section-subtitle {
      font-size: var(--text-xl);
      color: var(--gray-600);
      font-weight: var(--font-medium);
      max-width: 600px;
      margin: 0 auto;
      line-height: var(--leading-relaxed);
    }
  }

  .pain-points-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-12);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: var(--space-6);
    }
  }

  .pain-point-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-3xl);
    padding: var(--space-10);
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.1),
      0 1px 0px rgba(255, 255, 255, 0.4) inset,
      0 -1px 0px rgba(0, 0, 0, 0.05) inset;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    // 卡片装饰线条
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #ef4444, #f97316, #eab308, #22c55e);
      opacity: 0;
      transition: var(--transition-all);
    }

    // 悬浮光效
    &::after {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(239, 68, 68, 0.1) 0%, transparent 70%);
      opacity: 0;
      transition: var(--transition-all);
      pointer-events: none;
    }

    &:hover {
      transform: translateY(-8px) scale(1.02);
      box-shadow:
        0 32px 64px rgba(0, 0, 0, 0.15),
        0 1px 0px rgba(255, 255, 255, 0.6) inset;

      &::before {
        opacity: 1;
      }

      &::after {
        opacity: 1;
      }

      .point-icon {
        transform: scale(1.1) rotate(5deg);
        box-shadow:
          0 20px 40px rgba(239, 68, 68, 0.3),
          0 0 0 8px rgba(239, 68, 68, 0.1);
      }

      .point-title {
        color: #dc2626;
      }
    }

    .point-icon {
      width: 100px;
      height: 100px;
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto var(--space-8);
      color: var(--white);
      font-size: var(--text-4xl);
      box-shadow:
        0 16px 32px rgba(239, 68, 68, 0.2),
        0 0 0 4px rgba(239, 68, 68, 0.1);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;

      // 图标内部光效
      &::before {
        content: '';
        position: absolute;
        top: 20%;
        left: 20%;
        width: 60%;
        height: 60%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
        border-radius: 50%;
        opacity: 0.8;
      }
    }

    .point-title {
      font-size: var(--text-2xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-5);
      transition: var(--transition-all);
      letter-spacing: -0.01em;
    }

    .point-description {
      color: var(--gray-600);
      line-height: var(--leading-relaxed);
      font-size: var(--text-base);
      font-weight: var(--font-medium);
    }

    // 不同卡片的特色颜色
    &:nth-child(1) {
      .point-icon {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
        box-shadow:
          0 16px 32px rgba(59, 130, 246, 0.2),
          0 0 0 4px rgba(59, 130, 246, 0.1);
      }
      &:hover .point-title { color: #2563eb; }
      &::after {
        background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
      }
    }

    &:nth-child(2) {
      .point-icon {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
        box-shadow:
          0 16px 32px rgba(245, 158, 11, 0.2),
          0 0 0 4px rgba(245, 158, 11, 0.1);
      }
      &:hover .point-title { color: #d97706; }
      &::after {
        background: radial-gradient(circle, rgba(245, 158, 11, 0.1) 0%, transparent 70%);
      }
    }

    &:nth-child(3) {
      .point-icon {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
        box-shadow:
          0 16px 32px rgba(239, 68, 68, 0.2),
          0 0 0 4px rgba(239, 68, 68, 0.1);
      }
      &:hover .point-title { color: #dc2626; }
      &::after {
        background: radial-gradient(circle, rgba(239, 68, 68, 0.1) 0%, transparent 70%);
      }
    }

    &:nth-child(4) {
      .point-icon {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);
        box-shadow:
          0 16px 32px rgba(139, 92, 246, 0.2),
          0 0 0 4px rgba(139, 92, 246, 0.1);
      }
      &:hover .point-title { color: #7c3aed; }
      &::after {
        background: radial-gradient(circle, rgba(139, 92, 246, 0.1) 0%, transparent 70%);
      }
    }
  }
}

// 方案特点区域
.solution-features {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--gradient-subtle);

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--primary-alpha-20);
      border: 1px solid var(--primary-alpha-40);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--primary-blue);
      margin-bottom: var(--space-6);

      svg {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .section-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }

    .section-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }

  .features-grid {
    @include responsive-grid(auto-fit, 300px, var(--space-8));
  }

  .feature-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: var(--transition-all);
    position: relative;
    overflow: hidden;

    @include card-hover(1.02, -6px);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: var(--feature-color);
    }

    .feature-icon {
      width: 80px;
      height: 80px;
      background: var(--feature-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto var(--space-6);
      color: var(--white);
      font-size: var(--text-3xl);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .feature-title {
      font-size: var(--text-xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
    }

    .feature-description {
      color: var(--gray-600);
      line-height: var(--leading-relaxed);
      font-size: var(--text-sm);
    }
  }
}

// 核心解决方案区域
.core-solutions {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--gradient-subtle);

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--primary-alpha-20);
      border: 1px solid var(--primary-alpha-40);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--primary-blue);
      margin-bottom: var(--space-6);

      svg {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .section-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }

    .section-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }

  .solutions-grid {
    @include responsive-grid(auto-fit, 380px, var(--space-8));
  }

  .solution-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    cursor: pointer;
    transition: var(--transition-all);

    @include card-hover(1.02, -6px);

    .card-header {
      position: relative;
      padding: var(--space-8);
      background: var(--gradient-subtle);
      text-align: center;

      .solution-icon {
        width: 80px;
        height: 80px;
        background: var(--gradient-primary);
        border-radius: var(--radius-2xl);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--space-4);
        color: var(--white);
        font-size: var(--text-3xl);
        box-shadow: var(--glow-primary);
        transition: var(--transition-all);
      }

      .solution-category {
        display: inline-block;
        padding: var(--space-1) var(--space-3);
        background: var(--primary-alpha-20);
        color: var(--primary-blue);
        border-radius: var(--radius-2xl);
        font-size: var(--text-xs);
        font-weight: var(--font-bold);
      }
    }

    .card-content {
      padding: var(--space-6);

      .solution-title {
        font-size: var(--text-xl);
        font-weight: var(--font-bold);
        color: var(--gray-900);
        margin-bottom: var(--space-3);
        line-height: var(--leading-tight);
      }

      .solution-description {
        color: var(--gray-600);
        line-height: var(--leading-relaxed);
        margin-bottom: var(--space-6);
        font-size: var(--text-sm);
      }

      .solution-benefits {
        margin-bottom: var(--space-6);

        .benefits-title {
          font-size: var(--text-sm);
          font-weight: var(--font-bold);
          color: var(--gray-800);
          margin-bottom: var(--space-3);
        }

        .benefits-list {
          display: flex;
          flex-direction: column;
          gap: var(--space-2);

          .benefit {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: var(--text-sm);
            color: var(--gray-700);

            .benefit-icon {
              color: var(--success-green);
              font-size: var(--text-sm);
              flex-shrink: 0;
            }
          }
        }
      }

      .solution-tech {
        margin-bottom: var(--space-6);

        .tech-title {
          font-size: var(--text-sm);
          font-weight: var(--font-bold);
          color: var(--gray-800);
          margin-bottom: var(--space-3);
        }

        .tech-tags {
          display: flex;
          flex-wrap: wrap;
          gap: var(--space-2);

          .tech-tag {
            background: var(--crystal-blue);
            color: var(--primary-blue);
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-2xl);
            font-size: var(--text-xs);
            font-weight: var(--font-medium);
          }
        }
      }
    }

    .card-footer {
      padding: 0 var(--space-6) var(--space-6);
      display: flex;
      justify-content: space-between;
      align-items: center;

      .solution-metrics {
        display: flex;
        gap: var(--space-6);

        .metric {
          text-align: center;

          .metric-number {
            display: block;
            font-size: var(--text-lg);
            font-weight: var(--font-bold);
            color: var(--primary-blue);
          }

          .metric-label {
            font-size: var(--text-xs);
            color: var(--gray-500);
          }
        }
      }

      .explore-btn {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        padding: var(--space-2) var(--space-4);
        background: var(--gradient-primary);
        color: var(--white);
        border: none;
        border-radius: var(--radius-lg);
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        cursor: pointer;
        transition: var(--transition-all);

        .btn-arrow {
          font-size: var(--text-sm);
          transition: var(--transition-all);
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: var(--shadow-hover);

          .btn-arrow {
            transform: translateX(2px);
          }
        }
      }
    }

    &:hover {
      .card-header .solution-icon {
        transform: scale(1.1);
      }
    }
  }
}

// 应用场景区域
.application-scenarios {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--white);

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--primary-alpha-20);
      border: 1px solid var(--primary-alpha-40);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--primary-blue);
      margin-bottom: var(--space-6);

      svg {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .section-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }

    .section-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }

  .scenarios-grid {
    @include responsive-grid(auto-fit, 280px, var(--space-8));
  }

  .scenario-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: var(--transition-all);
    position: relative;
    overflow: hidden;

    @include card-hover(1.02, -6px);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 4px;
      background: var(--scenario-color);
    }

    .scenario-icon {
      width: 80px;
      height: 80px;
      background: var(--scenario-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto var(--space-6);
      color: var(--white);
      font-size: var(--text-3xl);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .scenario-title {
      font-size: var(--text-xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
    }

    .scenario-description {
      color: var(--gray-600);
      line-height: var(--leading-relaxed);
      font-size: var(--text-sm);
    }
  }
}

// CTA 区域
.cta-section {
  position: relative;
  @include section-padding(var(--space-20), var(--space-20));
  overflow: hidden;

  .cta-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
  }

  .cta-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      var(--primary-alpha-90) 0%,
      var(--corporate-blue) 100%
    );
  }

  .cta-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    grid-template-rows: repeat(5, 1fr);
    opacity: 0.1;

    .grid-item {
      border: 1px solid var(--white-alpha-20);
      transition: var(--transition-all);

      &:hover {
        background: var(--white-alpha-10);
      }
    }
  }

  .container {
    position: relative;
    z-index: 2;
  }

  .cta-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--space-12);
    align-items: center;
    color: var(--white);

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      text-align: center;
      gap: var(--space-8);
    }

    .cta-text {
      .cta-badge {
        display: inline-flex;
        align-items: center;
        gap: var(--space-2);
        padding: var(--space-2) var(--space-4);
        background: var(--white-alpha-20);
        border: 1px solid var(--white-alpha-30);
        border-radius: var(--radius-2xl);
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        margin-bottom: var(--space-6);
        @include glass-morphism(0.1, 16px, 0.2);

        svg {
          font-size: var(--text-lg);
          color: var(--electric-blue);
        }
      }

      .cta-title {
        font-size: var(--text-4xl);
        font-weight: var(--font-bold);
        margin-bottom: var(--space-6);
        line-height: var(--leading-tight);

        @media (max-width: 768px) {
          font-size: var(--text-3xl);
        }
      }

      .cta-subtitle {
        font-size: var(--text-lg);
        line-height: var(--leading-relaxed);
        opacity: 0.9;
        margin-bottom: var(--space-8);

        strong {
          color: var(--electric-blue);
          font-weight: var(--font-bold);
        }
      }

      .cta-benefits {
        display: flex;
        gap: var(--space-6);
        margin-bottom: var(--space-8);

        @media (max-width: 640px) {
          flex-direction: column;
          gap: var(--space-4);
        }

        .benefit-item {
          display: flex;
          align-items: center;
          gap: var(--space-2);
          font-size: var(--text-sm);
          font-weight: var(--font-medium);

          svg {
            font-size: var(--text-lg);
            color: var(--electric-blue);
          }
        }
      }
    }

    .cta-actions {
      display: flex;
      flex-direction: column;
      gap: var(--space-4);
      align-items: flex-end;

      @media (max-width: 968px) {
        align-items: center;
      }

      button {
        display: flex;
        align-items: center;
        gap: var(--space-3);
        padding: var(--space-4) var(--space-6);
        border-radius: var(--radius-2xl);
        font-size: var(--text-base);
        font-weight: var(--font-medium);
        cursor: pointer;
        transition: var(--transition-all);
        border: none;
        white-space: nowrap;

        &.large {
          padding: var(--space-5) var(--space-8);
          font-size: var(--text-lg);
        }

        .btn-icon {
          font-size: var(--text-xl);
        }

        .btn-arrow {
          font-size: var(--text-lg);
          transition: var(--transition-all);
        }

        &.btn-primary {
          background: var(--white);
          color: var(--primary-blue);

          &:hover {
            background: var(--crystal-blue);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);

            .btn-arrow {
              transform: translateX(4px);
            }
          }
        }

        &.btn-secondary {
          background: transparent;
          color: var(--white);
          border: 2px solid var(--white);

          &:hover {
            background: var(--white);
            color: var(--primary-blue);
            transform: translateY(-2px);
          }
        }
      }

      .contact-info {
        display: flex;
        flex-direction: column;
        gap: var(--space-2);
        margin-top: var(--space-4);

        .contact-item {
          display: flex;
          align-items: center;
          gap: var(--space-2);
          font-size: var(--text-sm);
          color: rgba(255, 255, 255, 0.8);

          svg {
            font-size: var(--text-base);
            color: var(--electric-blue);
          }
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 968px) {
  .hero-section {
    min-height: 80vh;

    .hero-title {
      font-size: var(--text-4xl);
    }

    .hero-subtitle {
      font-size: var(--text-base);
    }

    .hero-features {
      flex-direction: column;
      gap: var(--space-4);
    }
  }

  .core-solutions,
  .solution-features,
  .application-scenarios {
    .solutions-grid,
    .features-grid,
    .scenarios-grid {
      grid-template-columns: 1fr;
    }
  }
}

@media (max-width: 640px) {
  .hero-section {
    .hero-title {
      font-size: var(--text-3xl);
    }

    .hero-visual .visual-container {
      width: 250px;
      height: 250px;
    }
  }

  .core-solutions,
  .solution-features,
  .application-scenarios,
  .cta-section {
    .section-header .section-title {
      font-size: var(--text-3xl);
    }
  }
}
</style>
