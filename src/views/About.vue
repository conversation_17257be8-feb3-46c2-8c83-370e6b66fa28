<script setup>
import { Icon } from "@iconify/vue";
import { ref, onMounted } from "vue";

defineOptions({
  name: "About",
});

// 页面状态
const isLoaded = ref(false);

// 公司数据
const companyStats = ref([
  {
    icon: "mdi:calendar",
    number: "2024",
    label: "成立年份",
    suffix: "年",
  },
  {
    icon: "mdi:account-group",
    number: "50",
    label: "团队成员",
    suffix: "+",
  },
  {
    icon: "mdi:trophy",
    number: "10",
    label: "软件著作权",
    suffix: "+",
  },
  {
    icon: "mdi:handshake",
    number: "100",
    label: "合作客户",
    suffix: "+",
  },
]);

// 核心团队数据
const coreTeam = ref([
  {
    name: "技术研发团队",
    description:
      "来自河海大学等知名院校，专业背景涵盖水利工程、计算机科学等领域",
    icon: "mdi:code-tags",
    skills: ["AI算法", "大数据", "云计算", "物联网"],
  },
  {
    name: "项目管理团队",
    description: "多人具有高级项目管理师资格，丰富的水利信息化项目管理经验",
    icon: "mdi:account-tie",
    skills: ["项目管理", "需求分析", "质量控制", "风险管控"],
  },
  {
    name: "行业专家团队",
    description: "深耕水利行业多年，对业务场景和技术需求有深刻理解",
    icon: "mdi:school",
    skills: ["水利工程", "防汛减灾", "水资源管理", "生态保护"],
  },
]);

// 发展历程数据
const milestones = ref([
  {
    year: "2024",
    title: "公司成立",
    description: "怀川科技（北京）有限公司正式成立，获得中关村高新技术企业证书",
    icon: "mdi:rocket-launch",
    achievements: ["中关村高新技术企业认证", "10+软件著作权", "核心团队组建"],
  },
  {
    year: "2024",
    title: "技术突破",
    description: "在AI大模型、大数据分析等前沿技术领域取得重要进展",
    icon: "mdi:brain",
    achievements: ["AI算法优化", "大数据平台搭建", "云原生架构"],
  },
  {
    year: "2024",
    title: "市场拓展",
    description: "与多家水利管理部门建立合作关系，项目成功落地",
    icon: "mdi:trending-up",
    achievements: ["客户合作拓展", "项目成功交付", "行业影响力提升"],
  },
]);

// 企业文化数据
const cultureValues = ref([
  {
    icon: "mdi:lightbulb-on",
    title: "创新驱动",
    description: "持续技术创新，引领行业发展",
    details: ["前沿技术研究", "产品持续迭代", "创新思维培养"],
  },
  {
    icon: "mdi:account-heart",
    title: "客户至上",
    description: "以客户需求为导向，提供优质服务",
    details: ["深度需求理解", "定制化解决方案", "7×24小时支持"],
  },
  {
    icon: "mdi:handshake",
    title: "合作共赢",
    description: "与合作伙伴共同成长，实现价值共创",
    details: ["开放合作态度", "生态伙伴建设", "互利共赢理念"],
  },
  {
    icon: "mdi:leaf",
    title: "绿色发展",
    description: "致力于生态保护，促进可持续发展",
    details: ["环保理念践行", "绿色技术应用", "生态价值创造"],
  },
]);

// 事件处理
const handleContact = () => {
  console.log("联系我们");
};

const handleJoinUs = () => {
  console.log("加入我们");
};

// 页面加载动画
onMounted(() => {
  setTimeout(() => {
    isLoaded.value = true;
  }, 100);

  // 数字动画效果
  const animateNumbers = () => {
    const numbers = document.querySelectorAll(".stat-number[data-count]");
    numbers.forEach((number) => {
      const target = parseInt(number.getAttribute("data-count"));
      let current = 0;
      const increment = target / 50;
      const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
          current = target;
          clearInterval(timer);
        }
        number.textContent = Math.floor(current);
      }, 50);
    });
  };

  setTimeout(animateNumbers, 500);
});
</script>

<template>
  <div class="about" :class="{ loaded: isLoaded }">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-background">
        <div class="hero-overlay"></div>
        <div class="hero-particles">
          <div v-for="i in 12" :key="i" :class="`particle particle-${i}`"></div>
        </div>
        <div class="hero-grid">
          <div v-for="i in 20" :key="i" class="grid-item"></div>
        </div>
      </div>

      <div class="container">
        <div class="hero-content">
          <div class="hero-badge fade-in-up">
            <Icon icon="mdi:domain" class="badge-icon" />
            <span>关于怀川科技</span>
          </div>

          <h1 class="hero-title fade-in-up delay-1">
            <span class="title-line">怀数智之力</span>
            <span class="title-line title-highlight">护青绿山川</span>
          </h1>

          <p class="hero-subtitle fade-in-up delay-2">
            专注智慧水利行业的现代化科技型企业<br />
            <strong>以科技创新驱动水利行业数字化转型</strong>
          </p>

          <div class="hero-stats fade-in-up delay-3">
            <div
              class="stat-item"
              v-for="stat in companyStats"
              :key="stat.label"
            >
              <div class="stat-icon">
                <Icon :icon="stat.icon" />
              </div>
              <div class="stat-number" :data-count="stat.number">0</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </div>
        </div>

        <div class="hero-visual fade-in-up delay-4">
          <div class="visual-container">
            <div class="company-hub">
              <div class="hub-core">
                <Icon icon="mdi:water-circle" class="core-icon" />
                <div class="core-pulse"></div>
              </div>
              <div class="hub-ring ring-outer"></div>
              <div class="hub-ring ring-middle"></div>
              <div class="hub-ring ring-inner"></div>
            </div>

            <div class="value-constellation">
              <div class="constellation-node node-1">
                <div class="node-icon">
                  <Icon icon="mdi:lightbulb-on" />
                </div>
                <div class="node-label">创新</div>
              </div>
              <div class="constellation-node node-2">
                <div class="node-icon">
                  <Icon icon="mdi:account-heart" />
                </div>
                <div class="node-label">客户至上</div>
              </div>
              <div class="constellation-node node-3">
                <div class="node-icon">
                  <Icon icon="mdi:handshake" />
                </div>
                <div class="node-label">合作</div>
              </div>
              <div class="constellation-node node-4">
                <div class="node-icon">
                  <Icon icon="mdi:leaf" />
                </div>
                <div class="node-label">绿色</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 公司介绍 -->
    <section class="company-intro">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:office-building" />
            <span>公司简介</span>
          </div>
          <h2 class="section-title">怀川科技简介</h2>
          <p class="section-subtitle">专业的智慧水利解决方案提供商</p>
        </div>

        <div class="intro-content">
          <div class="intro-text">
            <div class="intro-paragraph">
              <div class="paragraph-icon">
                <Icon icon="mdi:target" />
              </div>
              <div class="paragraph-content">
                <h3>专业定位</h3>
                <p>
                  怀川科技（北京）有限公司，是一家专注于智慧水利行业的现代化科技型企业，主要面向水利行业管理部门用户，提供防洪减灾、水资源管理、水利工程运行管理、河湖管理、水环境保护等业务领域数字化建设相关的规划咨询、产品销售、软件研发和系统集成相关专业服务。
                </p>
              </div>
            </div>

            <div class="intro-paragraph">
              <div class="paragraph-icon">
                <Icon icon="mdi:account-group" />
              </div>
              <div class="paragraph-content">
                <h3>团队实力</h3>
                <p>
                  公司总部设置在北京，研发中心在江苏，成员主要毕业于河海大学等水利或计算机相关院校和专业，多人具有高级项目管理师资格，有丰富的水利信息化项目管理和实施经验。
                </p>
              </div>
            </div>

            <div class="intro-paragraph">
              <div class="paragraph-icon">
                <Icon icon="mdi:rocket-launch" />
              </div>
              <div class="paragraph-content">
                <h3>技术创新</h3>
                <p>
                  公司注重锐意革新，致力于利用先进信息技术包括AI大模型、大数据、云计算、卫星遥感、无人机&船等，提升水利行业数字化建设专业服务水平，促进人与自然和谐发展，已于2024年初获得中关村高新技术企业证书，并取得10余项相关软著。
                </p>
              </div>
            </div>
          </div>

          <div class="intro-visual">
            <div class="visual-container">
              <div class="tech-constellation">
                <div class="tech-node node-ai">
                  <Icon icon="mdi:brain" />
                  <span>AI大模型</span>
                </div>
                <div class="tech-node node-data">
                  <Icon icon="mdi:database" />
                  <span>大数据</span>
                </div>
                <div class="tech-node node-cloud">
                  <Icon icon="mdi:cloud" />
                  <span>云计算</span>
                </div>
                <div class="tech-node node-iot">
                  <Icon icon="mdi:wifi" />
                  <span>物联网</span>
                </div>
                <div class="tech-node node-satellite">
                  <Icon icon="mdi:satellite-variant" />
                  <span>遥感</span>
                </div>
                <div class="tech-node node-drone">
                  <Icon icon="mdi:quadcopter" />
                  <span>无人机</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 核心团队 -->
    <section class="core-team">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:account-group" />
            <span>核心团队</span>
          </div>
          <h2 class="section-title">专业团队实力</h2>
          <p class="section-subtitle">汇聚行业精英，打造专业服务团队</p>
        </div>

        <div class="team-grid">
          <div v-for="(team, index) in coreTeam" :key="index" class="team-card">
            <div class="team-visual">
              <div class="team-icon">
                <Icon :icon="team.icon" />
              </div>
              <div class="team-bg"></div>
            </div>

            <div class="team-content">
              <h3 class="team-name">{{ team.name }}</h3>
              <p class="team-description">{{ team.description }}</p>

              <div class="team-skills">
                <div class="skills-title">专业技能</div>
                <div class="skills-list">
                  <span
                    class="skill-tag"
                    v-for="skill in team.skills"
                    :key="skill"
                    >{{ skill }}</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 发展历程 -->
    <section class="development-timeline">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:timeline" />
            <span>发展历程</span>
          </div>
          <h2 class="section-title">成长足迹</h2>
          <p class="section-subtitle">记录每一个重要的发展节点</p>
        </div>

        <div class="timeline-container">
          <div class="timeline-track"></div>
          <div
            v-for="(milestone, index) in milestones"
            :key="index"
            class="milestone-item"
            :class="`milestone-${index + 1}`"
          >
            <div class="milestone-marker">
              <div class="marker-inner">
                <Icon :icon="milestone.icon" />
              </div>
              <div class="marker-year">{{ milestone.year }}</div>
            </div>

            <div class="milestone-content">
              <h3 class="milestone-title">{{ milestone.title }}</h3>
              <p class="milestone-description">{{ milestone.description }}</p>

              <div class="milestone-achievements">
                <div
                  class="achievement"
                  v-for="achievement in milestone.achievements"
                  :key="achievement"
                >
                  <Icon icon="mdi:check-circle" class="achievement-icon" />
                  <span>{{ achievement }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 企业文化 -->
    <section class="company-culture">
      <div class="container">
        <div class="section-header">
          <div class="header-badge">
            <Icon icon="mdi:heart" />
            <span>企业文化</span>
          </div>
          <h2 class="section-title">核心价值观</h2>
          <p class="section-subtitle">指引我们前进的价值理念</p>
        </div>

        <div class="culture-grid">
          <div
            v-for="(value, index) in cultureValues"
            :key="index"
            class="culture-card"
          >
            <div class="culture-visual">
              <div class="culture-number">
                {{ String(index + 1).padStart(2, "0") }}
              </div>
              <div class="culture-icon">
                <Icon :icon="value.icon" />
              </div>
              <div class="visual-bg"></div>
            </div>

            <div class="culture-content">
              <h3 class="culture-title">{{ value.title }}</h3>
              <p class="culture-description">{{ value.description }}</p>

              <div class="culture-details">
                <div
                  class="detail"
                  v-for="detail in value.details"
                  :key="detail"
                >
                  <Icon icon="mdi:arrow-right-circle" class="detail-icon" />
                  <span>{{ detail }}</span>
                </div>
              </div>
            </div>

            <div class="culture-decorator"></div>
          </div>
        </div>
      </div>
    </section>

    <!-- 愿景使命 -->
    <section class="vision-mission">
      <div class="vision-background">
        <div class="vision-overlay"></div>
        <div class="vision-pattern">
          <div v-for="i in 30" :key="i" class="pattern-dot"></div>
        </div>
      </div>

      <div class="container">
        <div class="vision-content">
          <div class="vision-item">
            <div class="vision-icon">
              <Icon icon="mdi:eye" />
            </div>
            <h2 class="vision-title">我们的愿景</h2>
            <p class="vision-text">
              成为智慧水利领域的领军企业，推动水利行业数字化转型，促进人与自然和谐发展。
            </p>
          </div>

          <div class="mission-item">
            <div class="mission-icon">
              <Icon icon="mdi:flag" />
            </div>
            <h2 class="mission-title">我们的使命</h2>
            <p class="mission-text">
              怀数智之力，护青绿山川，以科技创新驱动水利行业发展，构建水资源智能管理体系。
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA 区域 -->
    <section class="cta-section">
      <div class="cta-background">
        <div class="cta-overlay"></div>
        <div class="cta-grid">
          <div v-for="i in 20" :key="i" class="grid-item"></div>
        </div>
      </div>

      <div class="container">
        <div class="cta-content">
          <div class="cta-text">
            <div class="cta-badge">
              <Icon icon="mdi:handshake" />
              <span>携手合作</span>
            </div>
            <h2 class="cta-title">期待与您携手共创未来</h2>
            <p class="cta-subtitle">
              如果您对我们的服务感兴趣，或希望了解更多合作机会<br />
              <strong>欢迎随时与我们联系</strong>
            </p>

            <div class="contact-info">
              <div class="contact-item">
                <Icon icon="mdi:map-marker" />
                <div>
                  <span class="contact-label">公司总部</span>
                  <span class="contact-value">北京市海淀区中关村科技园</span>
                </div>
              </div>
              <div class="contact-item">
                <Icon icon="mdi:cog" />
                <div>
                  <span class="contact-label">研发中心</span>
                  <span class="contact-value">江苏省无锡市滨湖区雪浪小镇</span>
                </div>
              </div>
              <div class="contact-item">
                <Icon icon="mdi:email" />
                <div>
                  <span class="contact-label">电子邮箱</span>
                  <span class="contact-value"><EMAIL></span>
                </div>
              </div>
            </div>
          </div>

          <div class="cta-actions">
            <button class="btn-primary large" @click="handleContact">
              <Icon icon="mdi:phone" class="btn-icon" />
              <span>联系我们</span>
              <Icon icon="mdi:arrow-right" class="btn-arrow" />
            </button>
            <button class="btn-secondary large" @click="handleJoinUs">
              <Icon icon="mdi:account-plus" class="btn-icon" />
              <span>加入我们</span>
            </button>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style lang="scss" scoped>
@use "@/assets/styles/variables.scss" as *;

.about {
  min-height: 100vh;
  color: var(--gray-800);
  overflow-x: hidden;

  // 页面加载动画
  opacity: 0;
  transition: opacity var(--duration-700) var(--ease-out);

  &.loaded {
    opacity: 1;

    .fade-in-up {
      opacity: 1;
      transform: translateY(0);

      &.delay-1 {
        animation-delay: 0.1s;
      }
      &.delay-2 {
        animation-delay: 0.2s;
      }
      &.delay-3 {
        animation-delay: 0.3s;
      }
      &.delay-4 {
        animation-delay: 0.4s;
      }
    }
  }
}

// 通用动画类
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s var(--ease-smooth) forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 通用容器
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-6);

  @media (max-width: 768px) {
    padding: 0 var(--space-4);
  }
}

// 英雄区域
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;

  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-depth);
    z-index: -1;
  }

  .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      var(--primary-alpha-90) 0%,
      var(--corporate-blue) 100%
    );
    z-index: -1;
  }

  .hero-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .particle {
      position: absolute;
      background: var(--gradient-neon);
      border-radius: 50%;
      opacity: 0.3;

      &.particle-1 {
        width: 4px;
        height: 4px;
        top: 15%;
        left: 20%;
        @include floating-animation(3.2s, 12px, 0s);
      }
      &.particle-2 {
        width: 6px;
        height: 6px;
        top: 25%;
        left: 80%;
        @include floating-animation(4.1s, 18px, 0.5s);
      }
      &.particle-3 {
        width: 3px;
        height: 3px;
        top: 45%;
        left: 10%;
        @include floating-animation(3.8s, 15px, 1s);
      }
      &.particle-4 {
        width: 7px;
        height: 7px;
        top: 65%;
        left: 70%;
        @include floating-animation(4.5s, 22px, 1.5s);
      }
      &.particle-5 {
        width: 5px;
        height: 5px;
        top: 80%;
        left: 30%;
        @include floating-animation(3.6s, 14px, 2s);
      }
      &.particle-6 {
        width: 4px;
        height: 4px;
        top: 35%;
        left: 85%;
        @include floating-animation(4.2s, 16px, 2.5s);
      }
      &.particle-7 {
        width: 6px;
        height: 6px;
        top: 55%;
        left: 15%;
        @include floating-animation(3.9s, 20px, 0.8s);
      }
      &.particle-8 {
        width: 3px;
        height: 3px;
        top: 75%;
        left: 60%;
        @include floating-animation(4.3s, 11px, 1.8s);
      }
      &.particle-9 {
        width: 5px;
        height: 5px;
        top: 20%;
        left: 45%;
        @include floating-animation(3.7s, 17px, 2.2s);
      }
      &.particle-10 {
        width: 7px;
        height: 7px;
        top: 40%;
        left: 75%;
        @include floating-animation(4s, 19px, 1.2s);
      }
      &.particle-11 {
        width: 4px;
        height: 4px;
        top: 60%;
        left: 25%;
        @include floating-animation(4.4s, 13px, 0.3s);
      }
      &.particle-12 {
        width: 6px;
        height: 6px;
        top: 85%;
        left: 55%;
        @include floating-animation(3.5s, 21px, 2.8s);
      }
    }
  }

  .hero-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    grid-template-rows: repeat(5, 1fr);
    opacity: 0.1;

    .grid-item {
      border: 1px solid var(--primary-alpha-20);
      transition: var(--transition-all);

      &:hover {
        background: var(--primary-alpha-10);
      }
    }
  }

  .container {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    align-items: center;

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: var(--space-12);
      text-align: center;
    }
  }

  .hero-content {
    color: var(--white);
  }

  .hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    background: var(--primary-alpha-20);
    border: 1px solid var(--primary-alpha-40);
    border-radius: var(--radius-2xl);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    margin-bottom: var(--space-8);
    @include glass-morphism(0.15, 16px, 0.3);

    .badge-icon {
      color: var(--electric-blue);
      font-size: var(--text-lg);
    }
  }

  .hero-title {
    font-size: var(--text-6xl);
    font-weight: var(--font-bold);
    line-height: var(--leading-tight);
    margin-bottom: var(--space-8);

    .title-line {
      display: block;
      margin-bottom: var(--space-2);
    }

    .title-highlight {
      @include gradient-text(var(--gradient-neon));
    }

    @media (max-width: 768px) {
      font-size: var(--text-4xl);
    }
  }

  .hero-subtitle {
    font-size: var(--text-xl);
    line-height: var(--leading-relaxed);
    color: var(--sky-blue);
    margin-bottom: var(--space-10);

    strong {
      color: var(--white);
      @include gradient-text(var(--gradient-innovation));
    }
  }

  .hero-stats {
    display: flex;
    align-items: center;
    gap: var(--space-8);

    @media (max-width: 640px) {
      flex-direction: column;
      gap: var(--space-6);
    }

    .stat-item {
      text-align: center;

      .stat-icon {
        font-size: var(--text-2xl);
        color: var(--electric-blue);
        margin-bottom: var(--space-2);
      }

      .stat-number {
        font-size: var(--text-4xl);
        font-weight: var(--font-bold);
        color: var(--electric-blue);
        margin-bottom: var(--space-1);
        text-shadow: 0 0 10px var(--electric-blue);
      }

      .stat-label {
        font-size: var(--text-sm);
        color: var(--sky-blue);
        font-weight: var(--font-medium);
      }
    }
  }

  // 视觉区域
  .hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 600px;

    @media (max-width: 968px) {
      height: 400px;
      order: -1;
    }
  }

  .visual-container {
    position: relative;
    width: 500px;
    height: 500px;

    @media (max-width: 968px) {
      width: 350px;
      height: 350px;
    }
  }

  .company-hub {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .hub-core {
      position: relative;
      width: 120px;
      height: 120px;
      display: flex;
      align-items: center;
      justify-content: center;

      .core-icon {
        font-size: 60px;
        color: var(--electric-blue);
        z-index: 3;
        position: relative;
        @include floating-animation(3s, 8px);
        filter: drop-shadow(0 0 30px var(--electric-blue));
      }

      .core-pulse {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 120px;
        height: 120px;
        background: radial-gradient(
          circle,
          var(--electric-blue) 0%,
          transparent 70%
        );
        border-radius: 50%;
        opacity: 0.3;
        animation: pulse 2s infinite;
      }
    }

    .hub-ring {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border: 2px solid;
      border-radius: 50%;
      opacity: 0.6;

      &.ring-inner {
        width: 160px;
        height: 160px;
        border-color: var(--tech-alpha-50);
        animation: rotate 15s linear infinite;
      }

      &.ring-middle {
        width: 240px;
        height: 240px;
        border-color: var(--primary-alpha-30);
        animation: rotate 25s linear infinite reverse;
      }

      &.ring-outer {
        width: 320px;
        height: 320px;
        border-color: var(--electric-blue);
        opacity: 0.3;
        animation: rotate 35s linear infinite;
      }
    }
  }

  .value-constellation {
    .constellation-node {
      position: absolute;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--space-2);

      .node-icon {
        width: 50px;
        height: 50px;
        background: var(--gradient-glass);
        @include glass-morphism(0.2, 12px, 0.3);
        border-radius: var(--radius-xl);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--electric-blue);
        font-size: var(--text-xl);
        transition: var(--transition-all);

        &:hover {
          transform: scale(1.1);
          box-shadow: var(--glow-neon);
        }
      }

      .node-label {
        font-size: var(--text-xs);
        color: var(--sky-blue);
        font-weight: var(--font-medium);
        text-align: center;
        white-space: nowrap;
      }

      &.node-1 {
        top: 15%;
        left: 25%;
        @include floating-animation(4s, 12px, 0s);
      }

      &.node-2 {
        top: 20%;
        right: 20%;
        @include floating-animation(3.5s, 10px, 0.5s);
      }

      &.node-3 {
        right: 10%;
        top: 50%;
        @include floating-animation(4.2s, 15px, 1s);
      }

      &.node-4 {
        bottom: 20%;
        left: 20%;
        @include floating-animation(3.8s, 11px, 1.5s);
      }
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.1;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

// 公司介绍区域
.company-intro {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--white);

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--primary-alpha-20);
      border: 1px solid var(--primary-alpha-40);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--primary-blue);
      margin-bottom: var(--space-6);

      svg {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .section-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }

    .section-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }

  .intro-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    align-items: center;

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: var(--space-12);
    }

    .intro-text {
      .intro-paragraph {
        display: flex;
        gap: var(--space-4);
        margin-bottom: var(--space-8);
        padding: var(--space-6);
        background: var(--gradient-subtle);
        border-radius: var(--radius-2xl);
        border: 1px solid var(--gray-200);
        transition: var(--transition-all);

        &:hover {
          transform: translateY(-2px);
          box-shadow: var(--shadow-md);
        }

        .paragraph-icon {
          width: 50px;
          height: 50px;
          background: var(--gradient-primary);
          border-radius: var(--radius-xl);
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--white);
          font-size: var(--text-xl);
          flex-shrink: 0;
          box-shadow: var(--glow-primary);
        }

        .paragraph-content {
          flex: 1;

          h3 {
            font-size: var(--text-xl);
            font-weight: var(--font-bold);
            color: var(--gray-900);
            margin-bottom: var(--space-3);
          }

          p {
            color: var(--gray-600);
            line-height: var(--leading-relaxed);
            font-size: var(--text-base);
          }
        }
      }
    }

    .intro-visual {
      display: flex;
      justify-content: center;
      align-items: center;

      .visual-container {
        position: relative;
        width: 400px;
        height: 400px;
        background: var(--gradient-ocean);
        border-radius: var(--radius-3xl);
        overflow: hidden;
        box-shadow: var(--shadow-xl);

        @media (max-width: 768px) {
          width: 300px;
          height: 300px;
        }

        .tech-constellation {
          position: relative;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          .tech-node {
            position: absolute;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--space-2);
            padding: var(--space-3);
            background: var(--gradient-glass);
            @include glass-morphism(0.2, 12px, 0.3);
            border-radius: var(--radius-xl);
            color: var(--white);
            font-size: var(--text-sm);
            font-weight: var(--font-medium);
            transition: var(--transition-all);

            svg {
              font-size: var(--text-2xl);
              margin-bottom: var(--space-1);
            }

            &:hover {
              transform: scale(1.1);
              box-shadow: var(--glow-neon);
            }

            &.node-ai {
              top: 15%;
              left: 20%;
              @include floating-animation(4s, 12px, 0s);
            }

            &.node-data {
              top: 20%;
              right: 15%;
              @include floating-animation(3.5s, 10px, 0.5s);
            }

            &.node-cloud {
              right: 10%;
              top: 50%;
              @include floating-animation(4.2s, 15px, 1s);
            }

            &.node-iot {
              bottom: 20%;
              right: 20%;
              @include floating-animation(3.8s, 11px, 1.5s);
            }

            &.node-satellite {
              bottom: 15%;
              left: 15%;
              @include floating-animation(4.1s, 13px, 2s);
            }

            &.node-drone {
              left: 10%;
              top: 45%;
              @include floating-animation(3.7s, 9px, 2.5s);
            }
          }
        }
      }
    }
  }
}

// 核心团队区域
.core-team {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--gradient-subtle);

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--primary-alpha-20);
      border: 1px solid var(--primary-alpha-40);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--primary-blue);
      margin-bottom: var(--space-6);

      svg {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .section-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }

    .section-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }

  .team-grid {
    @include responsive-grid(auto-fit, 350px, var(--space-8));
  }

  .team-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: var(--transition-all);
    position: relative;
    overflow: hidden;

    @include card-hover(1.02, -6px);

    .team-visual {
      position: relative;
      margin-bottom: var(--space-6);
      text-align: center;

      .team-icon {
        width: 80px;
        height: 80px;
        background: var(--gradient-ocean);
        border-radius: var(--radius-2xl);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: var(--text-3xl);
        margin: 0 auto;
        box-shadow: var(--glow-primary);
        transition: var(--transition-all);
      }

      .team-bg {
        position: absolute;
        top: -10px;
        right: -10px;
        width: 100px;
        height: 100px;
        background: var(--gradient-glass);
        border-radius: 50%;
        opacity: 0.3;
        z-index: -1;
      }
    }

    .team-content {
      .team-name {
        font-size: var(--text-xl);
        font-weight: var(--font-bold);
        color: var(--gray-900);
        margin-bottom: var(--space-3);
        text-align: center;
      }

      .team-description {
        color: var(--gray-600);
        line-height: var(--leading-relaxed);
        margin-bottom: var(--space-6);
        font-size: var(--text-base);
        text-align: center;
      }

      .team-skills {
        .skills-title {
          font-size: var(--text-sm);
          font-weight: var(--font-bold);
          color: var(--gray-800);
          margin-bottom: var(--space-3);
          text-align: center;
        }

        .skills-list {
          display: flex;
          flex-wrap: wrap;
          gap: var(--space-2);
          justify-content: center;

          .skill-tag {
            background: var(--crystal-blue);
            color: var(--primary-blue);
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-2xl);
            font-size: var(--text-xs);
            font-weight: var(--font-medium);
          }
        }
      }
    }

    &:hover {
      .team-visual .team-icon {
        transform: scale(1.1);
      }
    }
  }
}

// 发展历程区域
.development-timeline {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--white);

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--primary-alpha-20);
      border: 1px solid var(--primary-alpha-40);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--primary-blue);
      margin-bottom: var(--space-6);

      svg {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .section-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }

    .section-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }

  .timeline-container {
    position: relative;
    max-width: 800px;
    margin: 0 auto;

    .timeline-track {
      position: absolute;
      left: 50%;
      top: 0;
      bottom: 0;
      width: 2px;
      background: var(--gradient-primary);
      transform: translateX(-50%);
      z-index: 1;

      @media (max-width: 768px) {
        left: 30px;
      }
    }

    .milestone-item {
      position: relative;
      display: flex;
      align-items: flex-start;
      margin-bottom: var(--space-16);
      z-index: 2;

      @media (max-width: 768px) {
        margin-left: var(--space-16);
      }

      &:nth-child(even) {
        flex-direction: row-reverse;

        @media (max-width: 768px) {
          flex-direction: row;
        }

        .milestone-content {
          text-align: right;
          margin-right: var(--space-8);
          margin-left: 0;

          @media (max-width: 768px) {
            text-align: left;
            margin-right: 0;
            margin-left: var(--space-4);
          }
        }
      }

      .milestone-marker {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        z-index: 3;

        .marker-inner {
          width: 80px;
          height: 80px;
          background: var(--gradient-primary);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--white);
          font-size: var(--text-2xl);
          box-shadow: var(--glow-primary);
          border: 4px solid var(--white);
        }

        .marker-year {
          position: absolute;
          bottom: -30px;
          background: var(--white);
          color: var(--primary-blue);
          padding: var(--space-1) var(--space-2);
          border-radius: var(--radius-lg);
          font-size: var(--text-xs);
          font-weight: var(--font-bold);
          box-shadow: var(--shadow-sm);
        }
      }

      .milestone-content {
        flex: 1;
        max-width: 300px;
        margin-left: var(--space-8);

        @media (max-width: 768px) {
          margin-left: var(--space-4);
          max-width: none;
        }

        .milestone-title {
          font-size: var(--text-xl);
          font-weight: var(--font-bold);
          color: var(--gray-900);
          margin-bottom: var(--space-3);
        }

        .milestone-description {
          color: var(--gray-600);
          line-height: var(--leading-relaxed);
          margin-bottom: var(--space-4);
          font-size: var(--text-base);
        }

        .milestone-achievements {
          display: flex;
          flex-direction: column;
          gap: var(--space-2);

          .achievement {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-size: var(--text-sm);
            color: var(--gray-700);

            .achievement-icon {
              color: var(--success-green);
              font-size: var(--text-sm);
              flex-shrink: 0;
            }
          }
        }
      }
    }
  }
}

// 企业文化区域
.company-culture {
  @include section-padding(var(--space-20), var(--space-20));
  background: var(--gradient-subtle);

  .section-header {
    text-align: center;
    margin-bottom: var(--space-16);

    .header-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      background: var(--primary-alpha-20);
      border: 1px solid var(--primary-alpha-40);
      border-radius: var(--radius-2xl);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
      color: var(--primary-blue);
      margin-bottom: var(--space-6);

      svg {
        font-size: var(--text-lg);
        color: var(--electric-blue);
      }
    }

    .section-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--space-4);
      @include gradient-text(var(--gradient-primary));
    }

    .section-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      font-weight: var(--font-medium);
    }
  }

  .culture-grid {
    @include responsive-grid(auto-fit, 300px, var(--space-8));
  }

  .culture-card {
    background: var(--white);
    padding: var(--space-8);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
    transition: var(--transition-all);

    @include card-hover(1.02, -6px);

    .culture-visual {
      position: relative;
      margin-bottom: var(--space-6);
      display: flex;
      align-items: center;
      gap: var(--space-4);

      .culture-number {
        font-size: var(--text-3xl);
        font-weight: var(--font-bold);
        color: var(--primary-alpha-30);
        line-height: 1;
      }

      .culture-icon {
        width: 60px;
        height: 60px;
        background: var(--gradient-ocean);
        border-radius: var(--radius-xl);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: var(--text-2xl);
        box-shadow: var(--glow-primary);
        position: relative;
        z-index: 2;
      }

      .visual-bg {
        position: absolute;
        top: -10px;
        right: -10px;
        width: 80px;
        height: 80px;
        background: var(--gradient-glass);
        border-radius: 50%;
        opacity: 0.3;
        z-index: 1;
      }
    }

    .culture-content {
      .culture-title {
        font-size: var(--text-xl);
        font-weight: var(--font-bold);
        color: var(--gray-900);
        margin-bottom: var(--space-3);
      }

      .culture-description {
        color: var(--gray-600);
        line-height: var(--leading-relaxed);
        margin-bottom: var(--space-4);
        font-size: var(--text-base);
      }

      .culture-details {
        display: flex;
        flex-direction: column;
        gap: var(--space-2);

        .detail {
          display: flex;
          align-items: center;
          gap: var(--space-2);
          font-size: var(--text-sm);
          color: var(--gray-700);

          .detail-icon {
            color: var(--success-green);
            font-size: var(--text-sm);
            flex-shrink: 0;
          }
        }
      }
    }

    .culture-decorator {
      position: absolute;
      bottom: -20px;
      right: -20px;
      width: 60px;
      height: 60px;
      background: var(--gradient-neon);
      border-radius: 50%;
      opacity: 0.1;
      transition: var(--transition-all);
    }

    &:hover {
      .culture-decorator {
        opacity: 0.2;
        transform: scale(1.2);
      }
    }
  }
}

// 愿景使命区域
.vision-mission {
  position: relative;
  @include section-padding(var(--space-20), var(--space-20));
  overflow: hidden;

  .vision-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
  }

  .vision-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      var(--primary-alpha-90) 0%,
      var(--corporate-blue) 100%
    );
  }

  .vision-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.1;

    .pattern-dot {
      position: absolute;
      width: 2px;
      height: 2px;
      background: var(--white);
      border-radius: 50%;

      &:nth-child(1) {
        top: 10%;
        left: 15%;
        animation: twinkle 3s infinite 0s;
      }
      &:nth-child(2) {
        top: 25%;
        left: 80%;
        animation: twinkle 4s infinite 0.5s;
      }
      &:nth-child(3) {
        top: 45%;
        left: 20%;
        animation: twinkle 3.5s infinite 1s;
      }
      &:nth-child(4) {
        top: 65%;
        left: 70%;
        animation: twinkle 4.5s infinite 1.5s;
      }
      &:nth-child(5) {
        top: 80%;
        left: 30%;
        animation: twinkle 3.2s infinite 2s;
      }
    }
  }

  .container {
    position: relative;
    z-index: 2;
  }

  .vision-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    color: var(--white);

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: var(--space-12);
      text-align: center;
    }

    .vision-item,
    .mission-item {
      text-align: center;
      padding: var(--space-8);
      background: var(--gradient-glass);
      @include glass-morphism(0.1, 20px, 0.2);
      border-radius: var(--radius-2xl);
      border: 1px solid var(--white-alpha-20);

      .vision-icon,
      .mission-icon {
        width: 80px;
        height: 80px;
        background: var(--gradient-neon);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--space-6);
        font-size: var(--text-3xl);
        color: var(--white);
        box-shadow: var(--glow-neon);
      }

      .vision-title,
      .mission-title {
        font-size: var(--text-2xl);
        font-weight: var(--font-bold);
        margin-bottom: var(--space-4);
        @include gradient-text(var(--gradient-neon));
      }

      .vision-text,
      .mission-text {
        font-size: var(--text-lg);
        line-height: var(--leading-relaxed);
        opacity: 0.9;
      }
    }
  }
}

@keyframes twinkle {
  0%,
  100% {
    opacity: 0.1;
  }
  50% {
    opacity: 1;
  }
}

// CTA 区域
.cta-section {
  position: relative;
  @include section-padding(var(--space-20), var(--space-20));
  overflow: hidden;

  .cta-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
  }

  .cta-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      var(--primary-alpha-90) 0%,
      var(--corporate-blue) 100%
    );
  }

  .cta-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(4, 1fr);
    opacity: 0.1;

    .grid-item {
      border: 1px solid var(--white-alpha-20);
      transition: var(--transition-all);

      &:hover {
        background: var(--white-alpha-10);
      }
    }
  }

  .container {
    position: relative;
    z-index: 2;
  }

  .cta-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--space-12);
    align-items: center;
    color: var(--white);

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      text-align: center;
      gap: var(--space-8);
    }

    .cta-text {
      .cta-badge {
        display: inline-flex;
        align-items: center;
        gap: var(--space-2);
        padding: var(--space-2) var(--space-4);
        background: var(--white-alpha-20);
        border: 1px solid var(--white-alpha-30);
        border-radius: var(--radius-2xl);
        font-size: var(--text-sm);
        font-weight: var(--font-medium);
        margin-bottom: var(--space-6);
        @include glass-morphism(0.1, 16px, 0.2);

        svg {
          font-size: var(--text-lg);
          color: var(--electric-blue);
        }
      }

      .cta-title {
        font-size: var(--text-4xl);
        font-weight: var(--font-bold);
        margin-bottom: var(--space-6);
        line-height: var(--leading-tight);

        @media (max-width: 768px) {
          font-size: var(--text-3xl);
        }
      }

      .cta-subtitle {
        font-size: var(--text-lg);
        line-height: var(--leading-relaxed);
        opacity: 0.9;
        margin-bottom: var(--space-8);

        strong {
          color: var(--electric-blue);
          font-weight: var(--font-bold);
        }
      }

      .contact-info {
        display: flex;
        flex-direction: column;
        gap: var(--space-4);

        .contact-item {
          display: flex;
          align-items: center;
          gap: var(--space-4);
          padding: var(--space-4);
          background: var(--gradient-glass);
          @include glass-morphism(0.1, 12px, 0.2);
          border-radius: var(--radius-xl);
          border: 1px solid var(--white-alpha-20);

          svg {
            font-size: var(--text-2xl);
            color: var(--electric-blue);
            flex-shrink: 0;
          }

          div {
            display: flex;
            flex-direction: column;
            gap: var(--space-1);

            .contact-label {
              font-size: var(--text-sm);
              font-weight: var(--font-bold);
              color: var(--electric-blue);
            }

            .contact-value {
              font-size: var(--text-base);
              opacity: 0.9;
            }
          }
        }
      }
    }

    .cta-actions {
      display: flex;
      flex-direction: column;
      gap: var(--space-4);
      align-items: flex-end;

      @media (max-width: 968px) {
        align-items: center;
      }

      button {
        display: flex;
        align-items: center;
        gap: var(--space-3);
        padding: var(--space-4) var(--space-6);
        border-radius: var(--radius-2xl);
        font-size: var(--text-base);
        font-weight: var(--font-medium);
        cursor: pointer;
        transition: var(--transition-all);
        border: none;
        white-space: nowrap;

        &.large {
          padding: var(--space-5) var(--space-8);
          font-size: var(--text-lg);
        }

        .btn-icon {
          font-size: var(--text-xl);
        }

        .btn-arrow {
          font-size: var(--text-lg);
          transition: var(--transition-all);
        }

        &.btn-primary {
          background: var(--white);
          color: var(--primary-blue);

          &:hover {
            background: var(--crystal-blue);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);

            .btn-arrow {
              transform: translateX(4px);
            }
          }
        }

        &.btn-secondary {
          background: transparent;
          color: var(--white);
          border: 2px solid var(--white);

          &:hover {
            background: var(--white);
            color: var(--primary-blue);
            transform: translateY(-2px);
          }
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 968px) {
  .hero-section {
    min-height: 80vh;

    .hero-title {
      font-size: var(--text-4xl);
    }

    .hero-subtitle {
      font-size: var(--text-base);
    }

    .hero-stats {
      flex-direction: column;
      gap: var(--space-4);
    }
  }

  .company-intro,
  .core-team,
  .company-culture {
    .intro-content,
    .team-grid,
    .culture-grid {
      grid-template-columns: 1fr;
    }
  }

  .development-timeline {
    .timeline-container {
      .milestone-item {
        &:nth-child(even) {
          flex-direction: row;

          .milestone-content {
            text-align: left;
            margin-right: 0;
            margin-left: var(--space-4);
          }
        }
      }
    }
  }
}

@media (max-width: 640px) {
  .hero-section {
    .hero-title {
      font-size: var(--text-3xl);
    }

    .hero-visual .visual-container {
      width: 250px;
      height: 250px;
    }
  }

  .company-intro,
  .core-team,
  .development-timeline,
  .company-culture,
  .vision-mission,
  .cta-section {
    .section-header .section-title {
      font-size: var(--text-3xl);
    }
  }

  .development-timeline {
    .timeline-container {
      .milestone-item {
        .milestone-marker .marker-inner {
          width: 60px;
          height: 60px;
          font-size: var(--text-xl);
        }
      }
    }
  }
}
</style>
