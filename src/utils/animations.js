/**
 * 专业动画工具库 - 替代setTimeout的优雅动画解决方案
 * 基于requestAnimationFrame和CSS Transitions的高性能动画系统
 */

/**
 * 使用requestAnimationFrame实现的延迟执行
 * 比setTimeout更精确，与浏览器刷新率同步
 * @param {Function} callback - 回调函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {number} - 可用于取消的ID
 */
export const rafDelay = (callback, delay) => {
  const start = performance.now();
  let rafId;

  const frame = (timestamp) => {
    if (timestamp - start >= delay) {
      callback();
    } else {
      rafId = requestAnimationFrame(frame);
    }
  };

  rafId = requestAnimationFrame(frame);
  return rafId;
};

/**
 * 动画序列执行器 - 无setTimeout的纯RAF实现
 * @param {Array} sequence - 动画序列 [{callback, delay}, ...]
 * @param {Object} options - 配置选项
 * @returns {Object} - 控制对象
 */
export const createAnimationSequence = (sequence, options = {}) => {
  const { onComplete = () => {}, onStart = () => {} } = options;

  let isRunning = false;
  let cancelIds = [];

  const start = () => {
    if (isRunning) return;

    isRunning = true;
    onStart();

    sequence.forEach((item, index) => {
      const id = rafDelay(() => {
        item.callback();

        // 检查是否为最后一个动画
        if (index === sequence.length - 1) {
          isRunning = false;
          onComplete();
        }
      }, item.delay);

      cancelIds.push(id);
    });
  };

  const cancel = () => {
    cancelIds.forEach((id) => cancelAnimationFrame(id));
    cancelIds = [];
    isRunning = false;
  };

  return { start, cancel, isRunning: () => isRunning };
};

/**
 * 交错动画 - 适用于列表项的依次显示
 * @param {Array} elements - 元素数组
 * @param {number} stagger - 交错延迟（毫秒）
 * @param {Function} animateElement - 单元素动画函数
 */
export const staggerAnimation = (elements, stagger = 100, animateElement) => {
  const sequence = elements.map((element, index) => ({
    callback: () => animateElement(element, index),
    delay: index * stagger,
  }));

  return createAnimationSequence(sequence);
};

/**
 * Vue 3组合式API的动画Hook
 * 注意：需要在Vue组件中使用，该函数依赖Vue的响应式系统
 * @param {Object} initialState - 初始状态
 * @returns {Function} - 返回创建动画状态的工厂函数
 */
export const createAnimationStateFactory = (initialState = {}) => {
  return (reactive) => {
    const state = reactive({ ...initialState });

    const setVisible = (key, visible = true, delay = 0) => {
      if (delay > 0) {
        rafDelay(() => {
          state[key] = visible;
        }, delay);
      } else {
        state[key] = visible;
      }
    };

    const setMultiple = (updates, staggerDelay = 100) => {
      Object.entries(updates).forEach(([key, visible], index) => {
        setVisible(key, visible, index * staggerDelay);
      });
    };

    const reset = () => {
      Object.keys(state).forEach((key) => {
        state[key] = false;
      });
    };

    return {
      state,
      setVisible,
      setMultiple,
      reset,
    };
  };
};

/**
 * IntersectionObserver动画 - 滚动触发动画
 * @param {Array} elements - 要观察的元素
 * @param {Function} callback - 动画回调
 * @param {Object} options - IntersectionObserver选项
 */
export const createScrollAnimation = (elements, callback, options = {}) => {
  const defaultOptions = {
    threshold: 0.1,
    rootMargin: "0px 0px -10% 0px",
    ...options,
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        callback(entry.target, entry);
        observer.unobserve(entry.target);
      }
    });
  }, defaultOptions);

  elements.forEach((element) => {
    observer.observe(element);
  });

  return observer;
};

/**
 * CSS变量动画 - 通过CSS自定义属性实现动画
 * @param {HTMLElement} element - 目标元素
 * @param {Object} variables - CSS变量对象
 * @param {number} duration - 动画持续时间
 */
export const animateCSSVariables = (element, variables, duration = 300) => {
  // 设置transition
  element.style.transition = Object.keys(variables)
    .map((key) => `${key} ${duration}ms ease`)
    .join(", ");

  // 应用CSS变量
  Object.entries(variables).forEach(([key, value]) => {
    element.style.setProperty(key, value);
  });

  // 清理transition
  rafDelay(() => {
    element.style.transition = "";
  }, duration + 50);
};

/**
 * 预定义动画类型
 */
export const AnimationType = {
  FADE_IN: "fadeIn",
  SLIDE_UP: "slideUp",
  SLIDE_DOWN: "slideDown",
  SLIDE_LEFT: "slideLeft",
  SLIDE_RIGHT: "slideRight",
  SCALE_IN: "scaleIn",
  ROTATE_IN: "rotateIn",
};

/**
 * 预定义缓动函数
 */
export const Easing = {
  LINEAR: "linear",
  EASE: "ease",
  EASE_IN: "ease-in",
  EASE_OUT: "ease-out",
  EASE_IN_OUT: "ease-in-out",
  BOUNCE: "cubic-bezier(0.68, -0.55, 0.265, 1.55)",
  SMOOTH: "cubic-bezier(0.4, 0, 0.2, 1)",
  SWIFT: "cubic-bezier(0.25, 0.46, 0.45, 0.94)",
};

// 默认导出一个便捷的动画管理器
export default {
  rafDelay,
  createAnimationSequence,
  staggerAnimation,
  createAnimationStateFactory,
  createScrollAnimation,
  animateCSSVariables,
  AnimationType,
  Easing,
};
