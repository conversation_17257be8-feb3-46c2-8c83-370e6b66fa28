{"compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["src/*"]}, "target": "esnext", "module": "esnext", "moduleResolution": "node", "jsx": "preserve", "strict": false, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "noImplicitAny": false, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "useDefineForClassFields": true}, "include": ["src/**/*.js", "src/**/*.vue", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "dist"]}