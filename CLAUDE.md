# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概览
这是怀川科技（北京）有限公司的官方网站，基于 Vue 3 + Vite 构建的现代化 SPA 应用。

## 开发命令
```bash
# 开发环境
pnpm dev

# 构建生产版本
pnpm build

# 预览构建结果
pnpm preview
```

## 项目架构

### 技术栈
- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **路由**: Vue Router 4
- **样式**: SCSS + CSS 变量设计系统
- **动画**: GSAP + 自定义动画库
- **图标**: @iconify/vue
- **UI组件**: Swiper (轮播)

⚠️：可以使用其他库，使用 pnpm install 添加依赖即可

### 目录结构
```
src/
├── assets/
│   ├── images/           # 静态图片资源
│   └── styles/
│       ├── variables.scss    # 设计系统变量（颜色、间距、动画等）
│       ├── reset.scss        # 样式重置
│       └── index.scss        # 全局样式
├── components/
│   └── NavBar.vue        # 主导航组件
├── views/                # 页面组件
│   ├── Home.vue         # 首页
│   ├── About.vue        # 关于我们
│   ├── Products.vue     # 产品页
│   └── Solutions.vue    # 解决方案页
├── router/
│   └── index.js         # 路由配置
├── utils/
│   └── animations.js    # 专业动画工具库
└── App.vue              # 根组件
```

### 设计系统
项目使用完整的设计系统，定义在 `src/assets/styles/variables.scss` 中：

- **颜色系统**: 山水主题色调（绿色系）+ 科技色彩（蓝色系）
- **间距系统**: 基于 4px 网格的一致性间距
- **字体大小**: 从 xs(12px) 到 6xl(60px) 的完整尺寸体系
- **阴影层级**: 5个层级的阴影系统 + 发光效果
- **动画过渡**: 3种速度 + 2种缓动曲线
- **SCSS混合器**: 玻璃效果、水波纹动画、浮动动画等

### 动画系统
自定义动画库 (`src/utils/animations.js`) 特性：
- 基于 `requestAnimationFrame` 的高性能动画
- 交错动画支持
- IntersectionObserver 滚动触发动画
- CSS 变量动画
- Vue 3 响应式状态管理

### 样式规范
- 使用 SCSS 模块化
- CSS 变量 + SCSS 变量双重系统
- 响应式设计（768px、480px 断点）
- 组件级作用域样式 (`scoped`)
- 统一的 BEM 命名约定

### 路由结构
- `/` - 首页
- `/about` - 关于我们
- `/products` - 产品展示
- `/solutions` - 解决方案

使用懒加载方式导入所有页面组件。

### 开发注意事项
- 路径别名：`@` 指向 `src` 目录
- 使用 CSS 变量进行主题定制
- 所有动画使用自定义动画库而非 setTimeout
- 图标统一使用 @iconify/vue
- 组件命名使用 PascalCase，文件名使用 PascalCase.vue

## 代码风格
- Vue 3 Composition API 语法
- 使用 `defineOptions` 设置组件名称
- SCSS 嵌套语法，遵循 BEM 方法论
- ES6+ 模块导入/导出
- 响应式设计优先